package cn.savas.hub.algocell.service.engine;

import cn.savas.hub.module.algocell.domain.*;
import cn.savas.hub.module.algocell.enums.MatchModeEnum;
import cn.savas.hub.module.algocell.enums.TermTypeEnum;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import cn.savas.hub.module.algocell.service.engine.MatchEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 匹配引擎测试类
 *
 * <AUTHOR>
 */
class MatchEngineTest {

    @InjectMocks
    private MatchEngine matchEngine;

    private KbRuntime kbRuntime;
    private List<Indicator> indicators;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 初始化知识库运行时
        kbRuntime = new KbRuntime();
        setupKnowledgeBase();

        // 初始化指标
        indicators = setupIndicators();
    }

    /**
     * 设置测试用的知识库
     */
    private void setupKnowledgeBase() {
        // 添加要素值
        FeatureValue carbonSteel = FeatureValue.builder()
            .featureCode("material")
            .valueCode("carbon_steel")
            .displayName("碳钢")
            .build();
        kbRuntime.addFeatureValue(carbonSteel);

        FeatureValue q345r = FeatureValue.builder()
            .featureCode("material")
            .valueCode("Q345R")
            .displayName("Q345R")
            .parentValueCode("carbon_steel")
            .build();
        kbRuntime.addFeatureValue(q345r);

        FeatureValue cl150 = FeatureValue.builder()
            .featureCode("pressure")
            .valueCode("CL150")
            .displayName("CL150")
            .build();
        kbRuntime.addFeatureValue(cl150);

        FeatureValue valve = FeatureValue.builder()
            .featureCode("valve_type")
            .valueCode("valve")
            .displayName("阀门")
            .build();
        kbRuntime.addFeatureValue(valve);

        // 添加术语条目
        TermEntry termQ345R = TermEntry.builder()
            .featureCode("material")
            .valueCode("Q345R")
            .type(TermTypeEnum.STRING)
            .expr("Q345R")
            .build();
        kbRuntime.addTermEntry(termQ345R);

        TermEntry termCL150 = TermEntry.builder()
            .featureCode("pressure")
            .valueCode("CL150")
            .type(TermTypeEnum.STRING)
            .expr("CL150")
            .build();
        kbRuntime.addTermEntry(termCL150);

        TermEntry termValve = TermEntry.builder()
            .featureCode("valve_type")
            .valueCode("valve")
            .type(TermTypeEnum.STRING)
            .expr("阀门")
            .build();
        kbRuntime.addTermEntry(termValve);

        // 添加正则术语
        TermEntry regexMaterial = TermEntry.builder()
            .featureCode("material")
            .valueCode("carbon_steel")
            .type(TermTypeEnum.REGEX)
            .expr("(20G|Q235|Q345R|A105)")
            .flags(0)
            .build();
        kbRuntime.addTermEntry(regexMaterial);
    }

    /**
     * 设置测试用的指标
     */
    private List<Indicator> setupIndicators() {
        Indicator indicator = Indicator.builder()
            .id(1L)
            .code("TEST-001")
            .name("测试指标-中低压阀门")
            .enabled(true)
            .build();

        // 添加指标要素要求
        IndicatorFeatureReq materialReq = IndicatorFeatureReq.builder()
            .indicatorId(1L)
            .featureCode("material")
            .requiredValueCode("carbon_steel")
            .matchMode(MatchModeEnum.ANCESTOR_OK)
            .weight(1.0)
            .required(true)
            .build();
        kbRuntime.addIndicatorRequirement(1L, materialReq);

        IndicatorFeatureReq pressureReq = IndicatorFeatureReq.builder()
            .indicatorId(1L)
            .featureCode("pressure")
            .requiredValueCode("CL150")
            .matchMode(MatchModeEnum.EXACT)
            .weight(1.0)
            .required(true)
            .build();
        kbRuntime.addIndicatorRequirement(1L, pressureReq);

        IndicatorFeatureReq valveReq = IndicatorFeatureReq.builder()
            .indicatorId(1L)
            .featureCode("valve_type")
            .requiredValueCode("valve")
            .matchMode(MatchModeEnum.EXACT)
            .weight(1.0)
            .required(true)
            .build();
        kbRuntime.addIndicatorRequirement(1L, valveReq);

        return Arrays.asList(indicator);
    }

    @Test
    void testPerfectMatch() {
        // 测试完美匹配
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("standard", "CL150");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());
        MatchResult result = results.get(0);

        assertEquals(1L, result.getIndicatorId());
        assertEquals(1.0, result.getMatchRate(), 0.001);
        assertTrue(result.canAutoBinding());
        assertTrue(result.getMissingFeatures().isEmpty());
    }

    @Test
    void testPartialMatch() {
        // 测试部分匹配
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        // 缺少压力信息

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());
        MatchResult result = results.get(0);

        assertEquals(1L, result.getIndicatorId());
        assertTrue(result.getMatchRate() < 1.0);
        assertFalse(result.canAutoBinding());
        assertFalse(result.getMissingFeatures().isEmpty());
        assertTrue(result.getMissingFeatures().contains("pressure=CL150"));
    }

    @Test
    void testRegexMatch() {
        // 测试正则匹配
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "A105材质");
        rowData.put("standard", "CL150");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());
        MatchResult result = results.get(0);

        assertEquals(1L, result.getIndicatorId());
        assertEquals(1.0, result.getMatchRate(), 0.001);

        // 验证证据中包含正则匹配
        boolean hasRegexEvidence = result.getEvidences().stream()
            .anyMatch(evidence -> "REGEX".equals(evidence.getFrom()));
        assertTrue(hasRegexEvidence);
    }

    @Test
    void testAncestorMatch() {
        // 测试祖先匹配（Q345R匹配carbon_steel要求）
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("standard", "CL150");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());
        MatchResult result = results.get(0);

        // Q345R是carbon_steel的子类，应该能匹配ANCESTOR_OK模式
        assertEquals(1.0, result.getMatchRate(), 0.001);
    }

    @Test
    void testNoMatch() {
        // 测试无匹配
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "泵");
        rowData.put("material", "不锈钢");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        // 应该没有匹配结果或匹配率很低
        assertTrue(results.isEmpty() || results.get(0).getMatchRate() < 0.5);
    }

    @Test
    void testEmptyRowData() {
        // 测试空行数据
        Map<String, String> rowData = new HashMap<>();

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertTrue(results.isEmpty());
    }

    @Test
    void testComplexData() {
        // 测试复杂数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("standard", "闸阀,CL150,A105/13Cr*STL,BB,OS&Y,RF,API 602||英吋直径:1||管表号/压力等级:CL150");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());
        MatchResult result = results.get(0);

        assertEquals(1.0, result.getMatchRate(), 0.001);

        // 验证证据数量
        assertFalse(result.getEvidences().isEmpty());

        // 验证包含多个匹配证据
        Set<String> evidenceTerms = new HashSet<>();
        for (Evidence evidence : result.getEvidences()) {
            evidenceTerms.add(evidence.getSourceTerm());
        }

        assertTrue(evidenceTerms.contains("阀门"));
        assertTrue(evidenceTerms.contains("Q345R"));
        assertTrue(evidenceTerms.contains("CL150"));
    }
}
