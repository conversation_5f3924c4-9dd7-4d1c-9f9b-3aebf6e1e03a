package cn.savas.hub.algocell.service.match;

import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchResultRespVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchRowReqVO;
import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.service.cache.MatchCacheService;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import cn.savas.hub.module.algocell.service.engine.MatchEngine;
import cn.savas.hub.module.algocell.service.kb.KbLoaderService;
import cn.savas.hub.module.algocell.service.kb.KbManagerService;
import cn.savas.hub.module.algocell.service.match.MatchServiceImpl;
import cn.savas.hub.module.algocell.service.monitor.PerformanceMonitorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 匹配服务实现测试类
 *
 * <AUTHOR>
 */
class MatchServiceImplTest {

    @Mock
    private MatchEngine matchEngine;

    @Mock
    private KbManagerService kbManagerService;

    @Mock
    private KbLoaderService kbLoaderService;

    @Mock
    private MatchCacheService matchCacheService;

    @Mock
    private PerformanceMonitorService performanceMonitorService;

    @InjectMocks
    private MatchServiceImpl matchService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testMatchRowSuccess() {
        // 准备测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        MatchRowReqVO request = MatchRowReqVO.builder()
            .rowData(rowData)
            .threshold(0.0)
            .maxResults(10)
            .includeEvidence(true)
            .build();

        // Mock依赖
        KbRuntime mockKbRuntime = new KbRuntime();
        Mockito.when(kbManagerService.getKbRuntime()).thenReturn(mockKbRuntime);
        Mockito.when(kbManagerService.getKbVersion()).thenReturn("TEST_VERSION");
        Mockito.when(matchCacheService.generateMatchCacheKey(ArgumentMatchers.any(), ArgumentMatchers.anyDouble(), ArgumentMatchers.anyInt())).thenReturn("test_key");
        Mockito.when(matchCacheService.getMatchResults(ArgumentMatchers.anyString())).thenReturn(null);
        Mockito.when(kbLoaderService.loadIndicators()).thenReturn(java.util.Collections.emptyList());
        Mockito.when(matchEngine.matchRow(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        MatchResultRespVO result = matchService.matchRow(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getProcessTime());
        assertEquals("TEST_VERSION", result.getKbVersion());
        assertEquals(0, result.getTotalMatches());

        // 验证方法调用
        Mockito.verify(kbManagerService).getKbRuntime();
        Mockito.verify(matchCacheService).generateMatchCacheKey(ArgumentMatchers.any(), ArgumentMatchers.anyDouble(), ArgumentMatchers.anyInt());
        Mockito.verify(matchCacheService).getMatchResults(ArgumentMatchers.anyString());
        Mockito.verify(performanceMonitorService).recordMatch(ArgumentMatchers.eq("matchRowData"), ArgumentMatchers.anyLong(), ArgumentMatchers.eq(true));
    }

    @Test
    void testMatchRowWithCache() {
        // 准备测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        MatchRowReqVO request = MatchRowReqVO.builder()
            .rowData(rowData)
            .build();

        // Mock缓存命中
        Mockito.when(kbManagerService.getKbRuntime()).thenReturn(new KbRuntime());
        Mockito.when(matchCacheService.generateMatchCacheKey(ArgumentMatchers.any(), ArgumentMatchers.anyDouble(), ArgumentMatchers.anyInt())).thenReturn("test_key");
        Mockito.when(matchCacheService.getMatchResults(ArgumentMatchers.anyString())).thenReturn(java.util.Collections.emptyList());
        Mockito.when(kbManagerService.getKbVersion()).thenReturn("TEST_VERSION");

        // 执行测试
        MatchResultRespVO result = matchService.matchRow(request);

        // 验证结果
        assertNotNull(result);

        // 验证没有调用匹配引擎（因为缓存命中）
        Mockito.verify(matchEngine, Mockito.never()).matchRow(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.verify(performanceMonitorService).recordMatch(ArgumentMatchers.eq("matchRowData"), ArgumentMatchers.anyLong(), ArgumentMatchers.eq(true));
    }

    @Test
    void testMatchRowKbNotReady() {
        // 准备测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        MatchRowReqVO request = MatchRowReqVO.builder()
            .rowData(rowData)
            .build();

        // Mock知识库未就绪
        Mockito.when(kbManagerService.getKbRuntime()).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            matchService.matchRow(request);
        });

        assertTrue(exception.getMessage().contains("知识库未就绪"));
        Mockito.verify(performanceMonitorService).recordMatch(ArgumentMatchers.eq("matchRowData"), ArgumentMatchers.anyLong(), ArgumentMatchers.eq(false));
    }

    @Test
    void testWarmUpSuccess() {
        // Mock依赖
        Mockito.when(kbManagerService.getKbRuntime()).thenReturn(new KbRuntime());
        Mockito.when(kbLoaderService.loadIndicators()).thenReturn(java.util.Collections.emptyList());
        Mockito.when(matchEngine.matchRow(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        boolean result = matchService.warmUp();

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        Mockito.verify(kbManagerService, Mockito.atLeastOnce()).getKbRuntime();
    }

    @Test
    void testWarmUpWithReload() {
        // Mock知识库初始为null，重载后可用
        Mockito.when(kbManagerService.getKbRuntime())
            .thenReturn(null)
            .thenReturn(new KbRuntime());
        Mockito.when(kbManagerService.reloadKnowledgeBase()).thenReturn(true);
        Mockito.when(kbLoaderService.loadIndicators()).thenReturn(java.util.Collections.emptyList());
        Mockito.when(matchEngine.matchRow(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        boolean result = matchService.warmUp();

        // 验证结果
        assertTrue(result);

        // 验证重载被调用
        Mockito.verify(kbManagerService).reloadKnowledgeBase();
    }

    @Test
    void testGetMatchStatistics() {
        // Mock依赖
        KbRuntime mockKbRuntime = new KbRuntime();
        Mockito.when(kbManagerService.getKbRuntime()).thenReturn(mockKbRuntime);
        Map<String, Object> mockStats = new HashMap<>();
        mockStats.put("matchCount", 100);
        Mockito.when(matchCacheService.getCacheStatistics()).thenReturn(mockStats);
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("avg_duration", 100.0);
        Mockito.when(performanceMonitorService.getPerformanceStatistics()).thenReturn(statistics);

        // 执行测试
        Map<String, Object> stats = matchService.getMatchStatistics();

        // 验证结果
        assertNotNull(stats);
        assertTrue(stats.containsKey("matchCount"));
        assertTrue(stats.containsKey("totalDuration"));
        assertTrue(stats.containsKey("averageDuration"));
        assertTrue(stats.containsKey("cache_hits"));
        assertTrue(stats.containsKey("avg_duration"));

        // 验证方法调用
        Mockito.verify(matchCacheService).getCacheStatistics();
        Mockito.verify(performanceMonitorService).getPerformanceStatistics();
    }

    @Test
    void testGetIndicatorRequirements() {
        // Mock依赖
        KbRuntime mockKbRuntime = new KbRuntime();
        Mockito.when(kbManagerService.getKbRuntime()).thenReturn(mockKbRuntime);

        // 执行测试
        java.util.List<String> requirements = matchService.getIndicatorRequirements(1L);

        // 验证结果
        assertNotNull(requirements);

        // 验证方法调用
        Mockito.verify(kbManagerService).getKbRuntime();
    }

    @Test
    void testMatchRowDataEmptyInput() {
        // 测试空输入
        java.util.List<MatchResult> results =
            matchService.matchRowData(null, 0.0, 10);

        assertTrue(results.isEmpty());

        results = matchService.matchRowData(new HashMap<>(), 0.0, 10);
        assertTrue(results.isEmpty());
    }
}
