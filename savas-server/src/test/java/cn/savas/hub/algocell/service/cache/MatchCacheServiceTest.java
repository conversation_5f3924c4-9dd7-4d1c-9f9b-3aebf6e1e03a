package cn.savas.hub.algocell.service.cache;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.service.cache.MatchCacheService;
import cn.savas.hub.module.algocell.service.cache.RedisCacheService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 简化版匹配缓存服务测试类
 *
 * <AUTHOR>
 */
class MatchCacheServiceTest {

    @Mock
    private AlgoCellProperties properties;

    @Mock
    private AlgoCellProperties.CacheConfig cacheConfig;

    @Mock
    private RedisCacheService redisCacheService;

    @InjectMocks
    private MatchCacheService matchCacheService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Mock配置
        Mockito.when(properties.getCache()).thenReturn(cacheConfig);
        Mockito.when(cacheConfig.getEnabled()).thenReturn(true);
        Mockito.when(cacheConfig.getEnableRedisCache()).thenReturn(true);
        Mockito.when(redisCacheService.isAvailable()).thenReturn(true);
    }

    @Test
    void testGetNormalizedTextHit() {
        // 准备测试数据
        String originalText = "阀门 Q345R";
        String expectedNormalized = "阀门Q345R";
        Mockito.when(redisCacheService.getCachedNormalizedText(originalText)).thenReturn(expectedNormalized);

        // 执行测试
        String result = matchCacheService.getNormalizedText(originalText);

        // 验证结果
        assertEquals(expectedNormalized, result);
        Mockito.verify(redisCacheService).getCachedNormalizedText(originalText);
    }

    @Test
    void testGetNormalizedTextMiss() {
        // 准备测试数据
        String originalText = "阀门 Q345R";
        Mockito.when(redisCacheService.getCachedNormalizedText(originalText)).thenReturn(null);

        // 执行测试
        String result = matchCacheService.getNormalizedText(originalText);

        // 验证结果
        assertNull(result);
        Mockito.verify(redisCacheService).getCachedNormalizedText(originalText);
    }

    @Test
    void testPutNormalizedText() {
        // 准备测试数据
        String originalText = "阀门 Q345R";
        String normalizedText = "阀门Q345R";

        // 执行测试
        matchCacheService.putNormalizedText(originalText, normalizedText);

        // 验证调用
        Mockito.verify(redisCacheService).cacheNormalizedText(originalText, normalizedText);
    }

    @Test
    void testGetMatchResultsHit() {
        // 准备测试数据
        String cacheKey = "test_key";
        List<MatchResult> expectedResults = new ArrayList<>();
        expectedResults.add(new MatchResult());
        Mockito.when(redisCacheService.getCachedMatchResults(ArgumentMatchers.eq(cacheKey), ArgumentMatchers.any(TypeReference.class)))
            .thenReturn(expectedResults);

        // 执行测试
        List<MatchResult> results = matchCacheService.getMatchResults(cacheKey);

        // 验证结果
        assertEquals(expectedResults, results);
        Mockito.verify(redisCacheService).getCachedMatchResults(ArgumentMatchers.eq(cacheKey), ArgumentMatchers.any(TypeReference.class));
    }

    @Test
    void testGetMatchResultsMiss() {
        // 准备测试数据
        String cacheKey = "test_key";
        Mockito.when(redisCacheService.getCachedMatchResults(ArgumentMatchers.eq(cacheKey), ArgumentMatchers.any(TypeReference.class)))
            .thenReturn(null);

        // 执行测试
        List<MatchResult> results = matchCacheService.getMatchResults(cacheKey);

        // 验证结果
        assertNull(results);
        Mockito.verify(redisCacheService).getCachedMatchResults(ArgumentMatchers.eq(cacheKey), ArgumentMatchers.any(TypeReference.class));
    }

    @Test
    void testPutMatchResults() {
        // 准备测试数据
        String cacheKey = "test_key";
        List<MatchResult> results = new ArrayList<>();
        results.add(new MatchResult());
        // 执行测试
        matchCacheService.putMatchResults(cacheKey, results);

        // 验证调用
        Mockito.verify(redisCacheService).cacheMatchResults(cacheKey, results);
    }

    @Test
    void testGenerateMatchCacheKey() {
        // 准备测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        double threshold = 0.8;
        int maxResults = 10;

        // 执行测试
        String cacheKey = matchCacheService.generateMatchCacheKey(rowData, threshold, maxResults);

        // 验证结果
        assertNotNull(cacheKey);
        assertTrue(cacheKey.startsWith("match:"));
        assertTrue(cacheKey.contains("material=Q345R"));
        assertTrue(cacheKey.contains("name=阀门"));
        assertTrue(cacheKey.contains("threshold=0.8"));
        assertTrue(cacheKey.contains("maxResults=10"));
    }

    @Test
    void testClearAll() {
        // 执行测试
        matchCacheService.clearAll();

        // 验证调用
        Mockito.verify(redisCacheService).clearKbCache();
    }

    @Test
    void testGetCacheStatistics() {
        // 执行测试
        Map<String, Object> stats = matchCacheService.getCacheStatistics();

        // 验证结果
        assertNotNull(stats);
        assertTrue(stats.containsKey("redis_enabled"));
        assertTrue(stats.containsKey("redis_available"));
        assertTrue(stats.containsKey("cache_type"));
        assertEquals("REDIS_ONLY", stats.get("cache_type"));
        assertTrue(stats.containsKey("hit_rate"));
        assertTrue(stats.containsKey("total_requests"));
    }

    @Test
    void testCacheDisabled() {
        // Mock缓存禁用
        Mockito.when(cacheConfig.getEnabled()).thenReturn(false);

        // 执行测试
        String result = matchCacheService.getNormalizedText("test");

        // 验证结果
        assertNull(result);

        // 验证没有调用Redis
        Mockito.verify(redisCacheService, Mockito.never()).getCachedNormalizedText(ArgumentMatchers.anyString());
    }

    @Test
    void testRedisNotAvailable() {
        // Mock Redis不可用
        Mockito.when(redisCacheService.isAvailable()).thenReturn(false);

        // 执行测试
        String result = matchCacheService.getNormalizedText("test");

        // 验证结果
        assertNull(result);

        // 验证没有调用Redis
        Mockito.verify(redisCacheService, Mockito.never()).getCachedNormalizedText(ArgumentMatchers.anyString());
    }

    @Test
    void testNullInputHandling() {
        // 测试null输入处理
        assertNull(matchCacheService.getNormalizedText(null));
        assertNull(matchCacheService.getMatchResults(null));
        assertNull(matchCacheService.generateMatchCacheKey(null, 0.8, 10));

        // 验证没有调用Redis
        Mockito.verify(redisCacheService, Mockito.never()).getCachedNormalizedText(ArgumentMatchers.any());
        Mockito.verify(redisCacheService, Mockito.never()).getCachedMatchResults(ArgumentMatchers.any(), ArgumentMatchers.any());
    }
}
