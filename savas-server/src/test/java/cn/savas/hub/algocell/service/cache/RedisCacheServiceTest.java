package cn.savas.hub.algocell.service.cache;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import cn.savas.hub.module.algocell.service.cache.RedisCacheService;
import cn.savas.hub.module.algocell.service.cache.RedisHealthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Redis缓存服务测试类
 *
 * 使用框架提供的Redis配置进行测试
 *
 * <AUTHOR>
 */
class RedisCacheServiceTest {

    @Mock
    private AlgoCellProperties properties;

    @Mock
    private AlgoCellProperties.CacheConfig cacheConfig;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private RedisHealthService redisHealthService;

    @InjectMocks
    private RedisCacheService redisCacheService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Mock配置
        Mockito.when(properties.getCache()).thenReturn(cacheConfig);
        Mockito.when(cacheConfig.getEnabled()).thenReturn(true);
        Mockito.when(cacheConfig.getEnableRedisCache()).thenReturn(true);
        Mockito.when(cacheConfig.getRedisKeyPrefix()).thenReturn("KB:");
        Mockito.when(cacheConfig.getRedisExpireHours()).thenReturn(24);

        // Mock Redis操作
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(redisHealthService.isRedisAvailable()).thenReturn(true);
    }

    @Test
    void testCacheKbVersion() {
        // 准备测试数据
        String version = "KB_20231201_001";

        // 执行测试
        redisCacheService.cacheKbVersion(version);

        // 验证调用
        Mockito.verify(valueOperations).set(ArgumentMatchers.eq("KB:VERSION"), ArgumentMatchers.eq(version), ArgumentMatchers.eq(24), ArgumentMatchers.eq(TimeUnit.HOURS));
    }

    @Test
    void testGetCachedKbVersion() {
        // 准备测试数据
        String expectedVersion = "KB_20231201_001";
        Mockito.when(valueOperations.get("KB:VERSION")).thenReturn(expectedVersion);

        // 执行测试
        String actualVersion = redisCacheService.getCachedKbVersion();

        // 验证结果
        assertEquals(expectedVersion, actualVersion);
        Mockito.verify(valueOperations).get("KB:VERSION");
    }

    @Test
    void testCacheFeatureWeights() throws Exception {
        // 准备测试数据
        Map<String, Double> featureWeights = new HashMap<>();
        featureWeights.put("material", 1.2);
        featureWeights.put("pressure", 1.5);

        String expectedJson = "{\"material\":1.2,\"pressure\":1.5}";
        Mockito.when(objectMapper.writeValueAsString(featureWeights)).thenReturn(expectedJson);

        // 执行测试
        redisCacheService.cacheFeatureWeights(featureWeights);

        // 验证调用
        Mockito.verify(objectMapper).writeValueAsString(featureWeights);
        Mockito.verify(valueOperations).set(ArgumentMatchers.eq("KB:FEATURE_WEIGHT"), ArgumentMatchers.eq(expectedJson), ArgumentMatchers.eq(24), ArgumentMatchers.eq(TimeUnit.HOURS));
    }

    @Test
    void testCacheValueToIndicators() throws Exception {
        // 准备测试数据
        String featureValueKey = "material::Q345R";
        Set<Long> indicatorIds = new HashSet<>();
        indicatorIds.add(1L);
        indicatorIds.add(2L);
        indicatorIds.add(3L);

        String expectedJson = "[1,2,3]";
        Mockito.when(objectMapper.writeValueAsString(indicatorIds)).thenReturn(expectedJson);

        // 执行测试
        redisCacheService.cacheValueToIndicators(featureValueKey, indicatorIds);

        // 验证调用
        Mockito.verify(objectMapper).writeValueAsString(indicatorIds);
        Mockito.verify(valueOperations).set(ArgumentMatchers.eq("KB:VAL2IND:material::Q345R"), ArgumentMatchers.eq(expectedJson), ArgumentMatchers.eq(24), ArgumentMatchers.eq(TimeUnit.HOURS));
    }

    @Test
    void testClearKbCache() {
        // 准备测试数据
        Set<String> keys = new HashSet<>();
        keys.add("KB:VERSION");
        keys.add("KB:FEATURE_WEIGHT");
        keys.add("KB:VAL2IND:material::Q345R");
        Mockito.when(redisTemplate.keys("KB:*")).thenReturn(keys);

        // 执行测试
        redisCacheService.clearKbCache();

        // 验证调用
        Mockito.verify(redisTemplate).keys("KB:*");
        Mockito.verify(redisTemplate).delete(keys);
    }

    @Test
    void testHasKbVersionUpdate() {
        // 准备测试数据
        String currentVersion = "KB_20231201_001";
        String cachedVersion = "KB_20231201_002";
        Mockito.when(valueOperations.get("KB:VERSION")).thenReturn(cachedVersion);

        // 执行测试
        boolean hasUpdate = redisCacheService.hasKbVersionUpdate(currentVersion);

        // 验证结果
        assertTrue(hasUpdate);
    }

    @Test
    void testHasKbVersionUpdateSameVersion() {
        // 准备测试数据
        String currentVersion = "KB_20231201_001";
        String cachedVersion = "KB_20231201_001";
        Mockito.when(valueOperations.get("KB:VERSION")).thenReturn(cachedVersion);

        // 执行测试
        boolean hasUpdate = redisCacheService.hasKbVersionUpdate(currentVersion);

        // 验证结果
        assertFalse(hasUpdate);
    }

    @Test
    void testRedisNotAvailable() {
        // Mock Redis不可用
        Mockito.when(redisHealthService.isRedisAvailable()).thenReturn(false);

        // 执行测试
        redisCacheService.cacheKbVersion("test");

        // 验证没有调用Redis操作
        Mockito.verify(valueOperations, Mockito.never()).set(ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.anyLong(), ArgumentMatchers.any(TimeUnit.class));
    }

    @Test
    void testRedisDisabled() {
        // Mock Redis功能禁用
        Mockito.when(cacheConfig.getEnableRedisCache()).thenReturn(false);

        // 执行测试
        redisCacheService.cacheKbVersion("test");

        // 验证没有调用Redis操作
        Mockito.verify(valueOperations, Mockito.never()).set(ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.anyLong(), ArgumentMatchers.any(TimeUnit.class));
    }

    @Test
    void testIsAvailable() {
        // 测试可用状态
        assertTrue(redisCacheService.isAvailable());

        // 测试不可用状态
        Mockito.when(redisHealthService.isRedisAvailable()).thenReturn(false);
        assertFalse(redisCacheService.isAvailable());
    }
}
