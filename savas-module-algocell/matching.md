下面给出一套“可直接落地”的完整方案：从数据建模 → 预处理索引 → 行级匹配管线 → 匹配率与决策 → 可解释结果与接口 → 性能与运维。核心用 Java 8，存储用 MySQL + Redis（可替换），不强制外部 NLP 依赖；若允许第三方包，建议接入分词库提升召回。

# **一、总体架构（离线构建 + 在线匹配，两级索引）**





1. **知识库离线构建器（KB Builder）**

- 读取行业知识库（支持字符串 & 正则），统一归一：**概念（feature）—值（value）—术语（term）** 的层级。

- 产出两类倒排索引：

  

  - **Term→(Feature,Value)**：词/短语/正则命中即可映射到“要素值”；
  - **(Feature,Value)→Indicator**：哪些指标要求该要素值。

  

- 生成**快速匹配结构**：

  

  - 字符串术语：AC/Trie（本方案自带轻量 Trie，可替换为 Aho-Corasick 实现）；
  - 正则术语：Pattern 预编译 + 白名单过滤；
  - **归一/同义词表**：如 “Q345R”“20G” → “碳钢”；“CL150”“Class150”→“CL150”。

  

- 将上述索引与结构序列化入 **MySQL（持久）** 与 **Redis（热数据）**。







1. **在线匹配服务（Match API）**







- 输入：一行表格数据（多列），列头即要素 key 候选。

- 处理：标准化 → 词典匹配 + 正则匹配 → 归一化为 (feature,value) 命中集 → 指标候选召回 → 评分与决策。

- 输出：

  

  - 每个候选指标的**匹配率**／细粒度得分；
  - 100% 匹配的指标直接绑定；
  - 可解释证据：命中词、所在列、规则来源（字符串/正则/同义词）。

  







# **二、数据建模（关系表 + 缓存键）**







## **2.1 MySQL 表（简化 DDL）**



```
-- 概念/要素（如 材质、压力、阀门类型）
CREATE TABLE kb_feature (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(64) UNIQUE NOT NULL,   -- 如: material, pressure, valve_type
  name VARCHAR(64) NOT NULL
);

-- 要素值（支持层级，如 材质->碳钢->Q345R）
CREATE TABLE kb_value (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  feature_code VARCHAR(64) NOT NULL,  -- 外键 kb_feature.code
  code VARCHAR(128) NOT NULL,         -- 归一化值，如: carbon_steel, Q345R
  name VARCHAR(128) NOT NULL,         -- 显示名，如: 碳钢, Q345R
  parent_code VARCHAR(128) NULL,      -- 父值（如 Q345R 的父为 carbon_steel）
  UNIQUE(feature_code, code)
);

-- 术语表（字符串/正则均支持）
CREATE TABLE kb_term (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  feature_code VARCHAR(64) NOT NULL,
  value_code VARCHAR(128) NOT NULL,       -- 命中即映射到该值
  term_type ENUM('STRING','REGEX') NOT NULL,
  expr VARCHAR(512) NOT NULL,             -- 词/短语 或 正则表达式
  flags INT DEFAULT 0                     -- 正则标志（如不区分大小写）
);

-- 指标与其要素要求
CREATE TABLE indicator (
  id BIGINT PRIMARY KEY,
  code VARCHAR(64) UNIQUE NOT NULL,
  name VARCHAR(256) NOT NULL
);
CREATE TABLE indicator_feature_req (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  indicator_id BIGINT NOT NULL,
  feature_code VARCHAR(64) NOT NULL,
  required_value_code VARCHAR(128) NULL,  -- 可为 NULL 表示仅要求有该要素
  match_mode ENUM('EXACT','ANCESTOR_OK','DESCENDANT_OK','ANY') DEFAULT 'EXACT'
  -- 例如: EXACT=必须命中该值；ANCESTOR_OK=父子均可；ANY=该要素出现即算
);
CREATE INDEX idx_ifr_indicator ON indicator_feature_req(indicator_id);
CREATE INDEX idx_ifr_feature ON indicator_feature_req(feature_code, required_value_code);
```

> 说明：



- > **层级**通过 kb_value.parent_code 支持；

- > match_mode 控制灵活匹配（如材质命中“Q345R”也可视作命中“碳钢”——ANCESTOR_OK）。







## **2.2 Redis Key 设计（示例）**





- KB:TRIE：字符串术语 Trie 的序列化（或自实现对象放本地内存 + 版本号）。
- KB:REGEX_LIST：List<CompiledRegex> 的 JSON/二进制序列化。
- KB:TERM2VAL:{hash}：哈希表（term→(feature,value)），小词典可直接放本地。
- KB:VAL2IND:{feature_code}:{value_code} → Set<indicator_id>。
- KB:FEATURE_WEIGHT：要素权重表（评分用）。
- KB:VERSION：热更新版本。







# **三、离线构建（关键要点）**





- **规范化**：

  

  - 全角转半角、繁简、大小写（保持工业代码大写如 CL150、Q345R）；
  - 统一标点与分隔符（,|/; 等）；
  - 单位标准化（英寸 ↔ in，压力等级 CL150）。

  

- **正则预编译**与**安全沙箱**：仅允许白名单字符集与锚点，防止灾难回溯。

- **层级回填**：生成 (feature,value) 时，附带其祖先链（如命中 Q345R，同时为“碳钢”的子值）。

- **倒排**：

  

  - Term→(Feature,Value) 用于第一跳召回；
  - (Feature,Value)→Indicator 用于快速拿到候选指标集合（Union）。

  







# **四、在线匹配流程（行级）**



```
输入行 -> 标准化 -> 列级文本拼接成 doc（保留列名）
      -> 词典匹配(Trie) + 正则匹配(Compiled Patterns)
      -> 命中证据集合 E = { (feature,value, sourceTerm, colName, pos, from=STRING|REGEX) }
      -> 归一化/层级提升（补充父值命中）
      -> 候选指标召回  C = ⋃ IND((f,v) ∈ E)
      -> 对每个指标 i ∈ C 计算匹配率/得分
      -> 100% 匹配直接绑定；否则按得分排序展示
```



# **五、评分与决策**





- **基础匹配率**：

  matchRate(i) = matchedRequiredFeatures(i) / totalRequiredFeatures(i)

- **细粒度得分**（用于排序与并列打破）：



```
score(i) = Σ_f [ w_f * score_f ]
其中 score_f:
  - 若 EXACT 且命中同值 = 1.0
  - 若 ANCESTOR_OK 且命中子值 = 1.0；命中父值=0.9
  - 若 DESCENDANT_OK 且命中父值 = 1.0；命中子值=0.95
  - 若 ANY 且该要素出现 = 0.7
冲突惩罚：同一 feature 命中多个互斥值 → -0.2
证据加成：同一 (f,v) 多处命中 → +0.05 * (occ-1) capped 0.15
```



- 

- **阈值策略**：

  

  - 100% → **自动关联**；
  - 80%~99% → 候选（需人工确认或规则白名单自动放行）；
  - <80% → 仅展示参考与缺失要素提示。

  







# **六、Java 8 核心代码（可直接落地）**





> 为了可运行与简洁，以下实现：



- > 自带一个轻量 Trie（仅做多模式字符串匹配）；

- > 正则使用 java.util.regex.Pattern；

- > 略去 DAO 细节，给出接口与内存结构；

- > 可替换分词器为外部库（例如 HanLP/Jieba），但非必需。



```
// === Domain ===
class FeatureValue {
    String featureCode;   // e.g., "material"
    String valueCode;     // e.g., "Q345R" or "carbon_steel"
    String displayName;   // e.g., "Q345R" or "碳钢"
    String parentValueCode; // optional
}

enum TermType { STRING, REGEX }

class TermEntry {
    String featureCode;
    String valueCode;
    TermType type;
    String expr;           // literal term or regex
    int flags;
}

class Indicator {
    long id;
    String code;
    String name;
}

enum MatchMode { EXACT, ANCESTOR_OK, DESCENDANT_OK, ANY }

class IndicatorFeatureReq {
    long indicatorId;
    String featureCode;
    String requiredValueCode; // nullable
    MatchMode mode;
}

// === Evidence ===
class Evidence {
    String featureCode;
    String valueCode;
    String sourceTerm;
    String colName;
    int start; int end;
    String from; // STRING or REGEX
}

// === Lightweight Trie (case-sensitive by default) ===
class Trie {
    static class Node {
        Map<Character, Node> children = new HashMap<>();
        List<String> outputs = new ArrayList<>(); // store terms that end here
    }
    private final Node root = new Node();
    void insert(String term) {
        Node cur = root;
        for (char c : term.toCharArray()) {
            cur = cur.children.computeIfAbsent(c, k -> new Node());
        }
        cur.outputs.add(term);
    }
    // Find all matches in text; returns (startIdx, term)
    List<AbstractMap.SimpleEntry<Integer, String>> findAll(String text) {
        List<AbstractMap.SimpleEntry<Integer, String>> res = new ArrayList<>();
        for (int i=0; i<text.length(); i++) {
            Node cur = root;
            for (int j=i; j<text.length(); j++) {
                char c = text.charAt(j);
                cur = cur.children.get(c);
                if (cur == null) break;
                if (!cur.outputs.isEmpty()) {
                    for (String t: cur.outputs) {
                        if (text.regionMatches(i, t, 0, t.length())) {
                            res.add(new AbstractMap.SimpleEntry<>(i, t));
                        }
                    }
                }
            }
        }
        return res;
    }
}

// === KB Runtime (内存结构，生产中可加版本/热更新) ===
class KbRuntime {
    Trie trie = new Trie(); // for STRING terms
    static class CompiledRegex {
        Pattern p; String featureCode; String valueCode; String expr;
    }
    List<CompiledRegex> regexList = new ArrayList<>();
    Map<String, List<FeatureValue>> valueByFeature = new HashMap<>(); // feature->values
    Map<String, FeatureValue> fvIndex = new HashMap<>(); // feature:value -> FeatureValue
    Map<String, List<TermEntry>> termIndex = new HashMap<>(); // term literal -> entries (STRING)
    Map<String, Set<Long>> val2Ind = new HashMap<>(); // feature:value -> indicator ids
    Map<Long, List<IndicatorFeatureReq>> indicatorReq = new HashMap<>();
    Map<String, Double> featureWeight = new HashMap<>();
    // parent mapping
    Map<String, String> parentOf = new HashMap<>(); // feature:value -> parentValueCode

    static String fvKey(String feature, String value) { return feature + "::" + value; }
}

// === Normalizer ===
class Normalizer {
    static String normalize(String s) {
        if (s == null) return "";
        String t = s.replaceAll("[\\u3000\\s]+"," "); // 多空白
        t = t.replace('，', ',').replace('；',';').replace('｜','|').replace('∫','∮'); // 示例
        t = t.replaceAll("（","(").replaceAll("）",")");
        // 特殊：工业常量保留大写
        // 对英寸/压力等可加入更丰富规则
        return t;
    }
}

// === Matcher Service ===
class MatcherService {
    private final KbRuntime kb;

    MatcherService(KbRuntime kb) { this.kb = kb; }

    static class MatchResult {
        long indicatorId;
        String indicatorCode;
        String indicatorName;
        double matchRate; // 0..1
        double score;     // 0..1+
        List<String> missingFeatures = new ArrayList<>();
        List<Evidence> evidences = new ArrayList<>();
    }

    public List<MatchResult> matchRow(Map<String, String> row, List<Indicator> indicators) {
        // 1) 拼接文档并保留列名（便于可解释）
        StringBuilder doc = new StringBuilder();
        Map<Integer,String> pos2col = new HashMap<>();
        int offset = 0;
        for (Map.Entry<String,String> e: row.entrySet()) {
            String col = e.getKey();
            String text = Normalizer.normalize(Objects.toString(e.getValue(),""));
            doc.append(col).append(":").append(text).append("\n");
            for (int i=offset; i<offset+text.length(); i++) pos2col.put(i, col);
            offset += (text.length()+1);
        }
        String document = doc.toString();

        // 2) 词典匹配（STRING）
        List<Evidence> evid = new ArrayList<>();
        for (AbstractMap.SimpleEntry<Integer,String> hit : kb.trie.findAll(document)) {
            String term = hit.getValue();
            List<TermEntry> entries = kb.termIndex.get(term);
            if (entries != null) {
                for (TermEntry te : entries) {
                    Evidence ev = new Evidence();
                    ev.featureCode = te.featureCode;
                    ev.valueCode = te.valueCode;
                    ev.sourceTerm = term;
                    ev.start = hit.getKey();
                    ev.end = hit.getKey() + term.length();
                    ev.colName = pos2col.getOrDefault(ev.start, "unknown");
                    ev.from = "STRING";
                    evid.add(ev);
                }
            }
        }
        // 3) 正则匹配
        for (KbRuntime.CompiledRegex cr : kb.regexList) {
            Matcher m = cr.p.matcher(document);
            while (m.find()) {
                Evidence ev = new Evidence();
                ev.featureCode = cr.featureCode;
                ev.valueCode = cr.valueCode;
                ev.sourceTerm = m.group(); // 原文证据
                ev.start = m.start();
                ev.end = m.end();
                ev.colName = pos2col.getOrDefault(ev.start, "unknown");
                ev.from = "REGEX";
                evid.add(ev);
            }
        }

        // 4) 归一化与层级补全（命中子值即补父值）
        Set<String> hitFVs = new HashSet<>();
        for (Evidence ev : evid) {
            hitFVs.add(KbRuntime.fvKey(ev.featureCode, ev.valueCode));
            // 补父链
            String cur = KbRuntime.fvKey(ev.featureCode, ev.valueCode);
            String parent = kb.parentOf.get(cur);
            while (parent != null) {
                hitFVs.add(KbRuntime.fvKey(ev.featureCode, parent));
                cur = KbRuntime.fvKey(ev.featureCode, parent);
                parent = kb.parentOf.get(cur);
            }
        }

        // 5) 候选指标召回
        Set<Long> candidateInd = new HashSet<>();
        for (String fv : hitFVs) {
            Set<Long> s = kb.val2Ind.get(fv);
            if (s != null) candidateInd.addAll(s);
        }
        // 若一个要素仅要求存在（ANY），也可通过 feature-level 倒排召回（此处简化直接在算分时处理）

        // 6) 对每个候选指标算匹配率与得分
        Map<Long, Indicator> indMap = new HashMap<>();
        for (Indicator in : indicators) indMap.put(in.id, in);

        List<MatchResult> results = new ArrayList<>();
        for (Long indId : candidateInd) {
            List<IndicatorFeatureReq> reqs = kb.indicatorReq.getOrDefault(indId, Collections.emptyList());
            if (reqs.isEmpty()) continue;

            int total = 0, matched = 0;
            double score = 0.0;
            List<String> missing = new ArrayList<>();

            for (IndicatorFeatureReq r : reqs) {
                total++;
                double wf = kb.featureWeight.getOrDefault(r.featureCode, 1.0);
                boolean ok = false;
                double sf = 0.0;

                // 命中判定
                if (r.requiredValueCode == null) {
                    // ANY: 该要素任意值出现即可
                    boolean present = hitFVs.stream().anyMatch(fv -> fv.startsWith(r.featureCode + "::"));
                    ok = present;
                    sf = present ? 0.7 : 0.0;
                } else {
                    String exactKey = KbRuntime.fvKey(r.featureCode, r.requiredValueCode);
                    switch (r.mode) {
                        case EXACT:
                            ok = hitFVs.contains(exactKey);
                            sf = ok ? 1.0 : 0.0;
                            break;
                        case ANCESTOR_OK:
                            // 命中该值或其任意子值
                            boolean exact = hitFVs.contains(exactKey);
                            boolean child = hitFVs.stream().anyMatch(fv ->
                                    fv.startsWith(r.featureCode + "::") && !fv.equals(exactKey) &&
                                    isDescendant(kb, r.featureCode, fv, r.requiredValueCode));
                            ok = exact || child;
                            sf = exact ? 0.9 : (child ? 1.0 : 0.0);
                            break;
                        case DESCENDANT_OK:
                            // 命中该值或其任意父值
                            boolean exact2 = hitFVs.contains(exactKey);
                            boolean parent = hitFVs.stream().anyMatch(fv ->
                                    fv.startsWith(r.featureCode + "::") && !fv.equals(exactKey) &&
                                    isAncestor(kb, r.featureCode, fv, r.requiredValueCode));
                            ok = exact2 || parent;
                            sf = exact2 ? 1.0 : (parent ? 0.95 : 0.0);
                            break;
                        case ANY:
                            boolean present2 = hitFVs.stream().anyMatch(fv -> fv.startsWith(r.featureCode + "::"));
                            ok = present2;
                            sf = present2 ? 0.7 : 0.0;
                            break;
                    }
                }

                if (ok) {
                    matched++;
                } else {
                    String miss = r.featureCode + (r.requiredValueCode != null ? ("=" + r.requiredValueCode) : "(ANY)");
                    missing.add(miss);
                }
                score += wf * sf;
            }

            MatchResult mr = new MatchResult();
            mr.indicatorId = indId;
            Indicator in = indMap.get(indId);
            if (in != null) { mr.indicatorCode = in.code; mr.indicatorName = in.name; }
            mr.matchRate = (double) matched / (double) total;
            // 归一化分数（可按需求缩放）
            mr.score = Math.min(1.0, score / Math.max(1.0, total));
            mr.missingFeatures = missing;
            mr.evidences = evid;
            results.add(mr);
        }

        // 排序：先匹配率，再得分，再指标复杂度（要素多者优先）
        results.sort(Comparator
                .comparingDouble((MatchResult x) -> x.matchRate).reversed()
                .thenComparingDouble(x -> x.score).reversed()
                .thenComparingInt(x -> kb.indicatorReq.getOrDefault(x.indicatorId, Collections.emptyList()).size()).reversed()
        );
        return results;
    }

    private static boolean isDescendant(KbRuntime kb, String feature, String fvKey, String ancestorVal) {
        // fvKey 如 "material::Q345R"
        String[] parts = fvKey.split("::", 2);
        if (parts.length != 2) return false;
        String cur = parts[1];
        String cursor = KbRuntime.fvKey(feature, cur);
        String parent = kb.parentOf.get(cursor);
        while (parent != null) {
            if (parent.equals(ancestorVal)) return true;
            cursor = KbRuntime.fvKey(feature, parent);
            parent = kb.parentOf.get(cursor);
        }
        return false;
    }
    private static boolean isAncestor(KbRuntime kb, String feature, String fvKey, String descendantVal) {
        // 是否 fvKey 的值是 descendantVal 的祖先
        String cursor = KbRuntime.fvKey(feature, descendantVal);
        String parent = kb.parentOf.get(cursor);
        while (parent != null) {
            if (KbRuntime.fvKey(feature, parent).equals(fvKey)) return true;
            cursor = KbRuntime.fvKey(feature, parent);
            parent = kb.parentOf.get(cursor);
        }
        return false;
    }
}
```

**如何装载知识库并编译：**

```
class KbLoader {
    public static KbRuntime loadFromDb(/* DataSource ds */) {
        KbRuntime kb = new KbRuntime();

        // 1) 加载 value 与层级
        // 伪代码：values.forEach(v -> kb.fvIndex.put(fvKey, fv));
        // 并填充 kb.parentOf(feature::child) = parentCode

        // 2) 加载术语，构建 Trie 与 Regex 列表
        // 对 STRING：kb.trie.insert(term); kb.termIndex.computeIfAbsent(term, ...).add(entry)
        // 对 REGEX：kb.regexList.add(CompiledRegex{ Pattern.compile(expr, flags), feature, value })

        // 3) 加载 (feature,value) -> indicators 倒排
        // kb.val2Ind.put(fvKey, Set<indicatorId>)

        // 4) 加载指标要素要求表
        // kb.indicatorReq.put(indId, listOfReqs)

        // 5) 要素权重（可从表或配置）
        // kb.featureWeight.put("material", 1.2); kb.featureWeight.put("pressure", 1.5)...

        return kb;
    }
}
```

**示例：把“Q345R”“CL150”“阀门”匹配到指标**

```
public class Demo {
    public static void main(String[] args) {
        KbRuntime kb = KbLoader.loadFromDb();
        MatcherService service = new MatcherService(kb);

        Map<String,String> row = new HashMap<>();
        row.put("name", "阀门");
        row.put("material", "Q345R");
        row.put("standard", "闸阀,CL150,A105/13Cr*STL,BB,OS&Y,RF,API 602||英吋直径:1||管表号/压力等级:CL150");

        // indicators 可从 DB 查出涉及到命中的 (feature,value) 的那一批
        List<Indicator> indicators = /* dao.getIndicatorsByFeatureValues(...) */ new ArrayList<>();

        List<MatcherService.MatchResult> res = service.matchRow(row, indicators);
        // 取 100% 的自动绑定
        List<MatcherService.MatchResult> full = new ArrayList<>();
        for (MatcherService.MatchResult r : res) if (r.matchRate >= 1.0) full.add(r);
        // 按需输出/入库
    }
}
```



# **七、指标侧配置策略（满足“多要素全匹配=100%”）**





- 指标库样例：{"id":23,"code":"2-5001","name":"化工、炼油 中低压阀门DN15~DN50"}

  

  - 要素要求（示例）：

  



```
(material = carbon_steel, mode=ANCESTOR_OK)
(pressure = CL150, mode=EXACT)
(valve_type = valve, mode=EXACT)
```



- 

  - 
  - 若命中 Q345R（碳钢子类）+ CL150 + 阀门 → **匹配率 100%**，自动绑定。

  







# **八、冲突处理与解释性**





- **冲突检测**：同一要素多值（如同时出现“碳钢”“不锈钢”），按规则：

  

  1. 列优先（若 material 列命中 > 其他列命中）；
  2. 置信度（正则 > 精确词 > 分词近似）；
  3. 更具体的值优先（子值 > 父值）。

  

- **解释输出**：对每个要素展示：命中的原文、列名、规则类型（STRING/REGEX）、采用/舍弃原因。







# **九、性能与规模**





- **QPS**：纯内存 Trie + 预编译正则，单实例可达万级 tokens/s；

- **批处理**：多线程（ForkJoin/线程池）+ 行级并行；

- **缓存**：

  

  - 行文本的标准化结果短期缓存（LRU）；
  - 指标倒排 val2Ind 常驻本地并以版本号热更新；

  

- **热更新**：

  

  - 每次 KB 发布写 KB:VERSION；
  - 实例轮询版本变更，重新拉取并原子替换内存结构。

  

- **正则成本控制**：先按字符集/前缀做粗过滤，再跑 Pattern；对超长文本设**限时**与**匹配上限**。







# **十、接口与落库**





- **API**：

  

  - POST /match/row：输入单行（JSON 列→值），返回候选指标列表（含匹配率、证据）。
  - POST /match/batch：批量行。
  - GET /kb/version、POST /kb/reload。

  

- **结果落库**：

  

  - match_result (row_id, indicator_id, match_rate, score, status=CONFIRMED|AUTO)
  - match_evidence (result_id, feature_code, value_code, source_term, col, rule_type)

  







# **十一、扩展与优化**

- 暂无

------





## **最小可用数据样例（便于试跑）**





**kb_value：**



- ("material","carbon_steel","碳钢", null)
- ("material","Q345R","Q345R","carbon_steel")
- ("pressure","CL150","CL150", null)
- ("valve_type","valve","阀门", null)





**kb_term：**



- STRING: (material,Q345R,"Q345R")
- STRING: (valve_type,valve,"阀门")
- STRING: (pressure,CL150,"CL150")
- REGEX : (material,carbon_steel,"(20G|Q235|Q345R|A105)")
- REGEX : (material,carbon_steel,"(9948|8163|3087|5310|6479)\\((20)\\)") // 示例，按实际规则调整





**indicator_feature_req：**



- 对 id=23:

  

  - (material, carbon_steel, ANCESTOR_OK)
  - (pressure, CL150, EXACT)
  - (valve_type, valve, EXACT)

  





------





### **结语**





这套方案保留了你们“要素全量匹配=100% 自动关联”的业务定义，同时通过**两级倒排 + Trie/正则**实现高效召回与可解释对齐；引入**层级/模式化匹配**解决“Q345R→碳钢”这类行业先验；并提供**得分与冲突**机制，以便中间态人工确认与持续优化。

直接按上文 DDL 与 Java 核心类落盘/落码，即可构建出最小可用版本，再按业务量级逐步引入缓存、学习排序与向量兜底。