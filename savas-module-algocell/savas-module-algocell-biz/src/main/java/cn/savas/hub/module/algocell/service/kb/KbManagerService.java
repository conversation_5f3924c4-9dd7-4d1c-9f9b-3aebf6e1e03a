package cn.savas.hub.module.algocell.service.kb;

import cn.savas.hub.module.algocell.service.cache.RedisCacheService;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 知识库管理服务
 *
 * 负责知识库的加载、更新、版本管理等
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KbManagerService {

    @Resource
    private KbLoaderService kbLoaderService;

    @Resource
    private RedisCacheService redisCacheService;

    /**
     * 当前知识库运行时
     */
    private volatile KbRuntime currentKbRuntime;

    /**
     * 知识库状态
     */
    private volatile String status = "LOADING";

    /**
     * 读写锁，保证线程安全
     */
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * 初始化时加载知识库
     */
    @PostConstruct
    public void init() {
        try {
            log.info("初始化知识库管理服务...");
            reloadKnowledgeBase();
        } catch (Exception e) {
            log.error("知识库初始化失败", e);
            status = "ERROR";
        }
    }

    /**
     * 获取知识库运行时
     *
     * @return 当前的知识库运行时
     */
    public KbRuntime getKbRuntime() {
        lock.readLock().lock();
        try {
            return currentKbRuntime;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 重新加载知识库
     *
     * @return 是否成功
     */
    public boolean reloadKnowledgeBase() {
        lock.writeLock().lock();
        try {
            log.info("开始重新加载知识库...");
            status = "LOADING";

            // 从数据库加载新的知识库
            KbRuntime newKbRuntime = kbLoaderService.loadFromDatabase();

            // 原子替换
            KbRuntime oldKbRuntime = currentKbRuntime;
            currentKbRuntime = newKbRuntime;

            // 清理旧的运行时
            if (oldKbRuntime != null) {
                oldKbRuntime.clear();
            }

            // 缓存新版本到Redis
            cacheKbDataToRedis(newKbRuntime);

            status = "READY";
            log.info("知识库重新加载完成，版本: {}", newKbRuntime.getVersion());

            return true;

        } catch (Exception e) {
            log.error("知识库重新加载失败", e);
            status = "ERROR";
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 检查知识库更新
     *
     * @return 是否有更新
     */
    public boolean checkForUpdates() {
        try {
            // 检查Redis中是否有新版本
            if (redisCacheService.isAvailable()) {
                String currentVersion = getKbVersion();
                return redisCacheService.hasKbVersionUpdate(currentVersion);
            }

            // 这里可以实现数据库版本检查逻辑
            // 比如检查数据库中的版本号或最后更新时间
            return false;
        } catch (Exception e) {
            log.warn("检查知识库更新失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取知识库状态
     *
     * @return 状态字符串
     */
    public String getStatus() {
        return status;
    }

    /**
     * 获取知识库版本
     *
     * @return 版本号
     */
    public String getKbVersion() {
        KbRuntime runtime = getKbRuntime();
        return runtime != null ? runtime.getVersion() : "UNKNOWN";
    }

    /**
     * 清理知识库缓存
     *
     * @return 是否成功
     */
    public boolean clearCache() {
        lock.writeLock().lock();
        try {
            if (currentKbRuntime != null) {
                currentKbRuntime.clear();
                currentKbRuntime = null;
            }
            status = "CLEARED";
            log.info("知识库缓存已清理");
            return true;
        } catch (Exception e) {
            log.error("清理知识库缓存失败", e);
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取知识库内存使用情况
     *
     * @return 内存使用量（MB）
     */
    public double getMemoryUsage() {
        KbRuntime runtime = getKbRuntime();
        if (runtime == null) {
            return 0.0;
        }

        // 简单估算内存使用量
        // 实际项目中可以使用更精确的内存测量工具
        Runtime jvmRuntime = Runtime.getRuntime();
        long totalMemory = jvmRuntime.totalMemory();
        long freeMemory = jvmRuntime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        return usedMemory / (1024.0 * 1024.0); // 转换为MB
    }

    /**
     * 判断知识库是否就绪
     *
     * @return 是否就绪
     */
    public boolean isReady() {
        return "READY".equals(status) && currentKbRuntime != null;
    }

    /**
     * 判断知识库是否正在加载
     *
     * @return 是否正在加载
     */
    public boolean isLoading() {
        return "LOADING".equals(status);
    }

    /**
     * 判断知识库是否有错误
     *
     * @return 是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(status);
    }

    /**
     * 将知识库数据缓存到Redis
     *
     * @param kbRuntime 知识库运行时
     */
    private void cacheKbDataToRedis(KbRuntime kbRuntime) {
        if (!redisCacheService.isAvailable() || kbRuntime == null) {
            return;
        }

        try {
            // 缓存知识库版本
            redisCacheService.cacheKbVersion(kbRuntime.getVersion());

            // 缓存要素权重表
            Map<String, Double> featureWeights = kbRuntime.getFeatureWeights();
            if (featureWeights != null && !featureWeights.isEmpty()) {
                redisCacheService.cacheFeatureWeights(featureWeights);
            }

            // 缓存倒排索引
            Map<String, Set<Long>> valueToIndicators = kbRuntime.getValueToIndicators();
            if (valueToIndicators != null) {
                for (Map.Entry<String, Set<Long>> entry : valueToIndicators.entrySet()) {
                    redisCacheService.cacheValueToIndicators(entry.getKey(), entry.getValue());
                }
            }

            log.info("知识库数据已缓存到Redis");

        } catch (Exception e) {
            log.warn("缓存知识库数据到Redis失败: {}", e.getMessage());
        }
    }
}
