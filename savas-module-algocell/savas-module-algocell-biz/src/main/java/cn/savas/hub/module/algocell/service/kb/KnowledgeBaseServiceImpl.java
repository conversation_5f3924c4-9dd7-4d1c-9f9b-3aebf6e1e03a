package cn.savas.hub.module.algocell.service.kb;

import cn.savas.hub.module.algocell.controller.admin.knowledge.vo.KbVersionRespVO;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 知识库服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Resource
    private KbManagerService kbManagerService;

    @Override
    public KbVersionRespVO getVersion() {
        KbRuntime kbRuntime = kbManagerService.getKbRuntime();
        String status = kbManagerService.getStatus();

        KbVersionRespVO.KbVersionRespVOBuilder builder = KbVersionRespVO.builder()
            .currentVersion(kbManagerService.getKbVersion())
            .status(status)
            .memoryUsageMB(kbManagerService.getMemoryUsage())
            .lastUpdateTime(LocalDateTime.now());

        if (kbRuntime != null) {
            Map<String, Object> stats = kbRuntime.getStatistics();

            builder.featureCount(getIntValue(stats, "featureCount"))
                   .valueCount(getIntValue(stats, "valueCount"))
                   .termCount(getIntValue(stats, "stringTermCount") + getIntValue(stats, "regexTermCount"))
                   .stringTermCount(getIntValue(stats, "stringTermCount"))
                   .regexTermCount(getIntValue(stats, "regexTermCount"))
                   .indicatorCount(getIntValue(stats, "indicatorCount"));

            if (stats.containsKey("lastUpdateTime")) {
                long timestamp = (Long) stats.get("lastUpdateTime");
                builder.releaseTime(LocalDateTime.now()); // 简化处理
            }
        } else {
            builder.featureCount(0)
                   .valueCount(0)
                   .termCount(0)
                   .stringTermCount(0)
                   .regexTermCount(0)
                   .indicatorCount(0);
        }

        // 设置状态描述
        String statusDescription = getStatusDescription(status);
        builder.statusDescription(statusDescription);

        return builder.build();
    }

    @Override
    public boolean reload() {
        try {
            log.info("收到知识库重新加载请求");
            return kbManagerService.reloadKnowledgeBase();
        } catch (Exception e) {
            log.error("知识库重新加载失败", e);
            return false;
        }
    }

    @Override
    public boolean checkForUpdates() {
        try {
            return kbManagerService.checkForUpdates();
        } catch (Exception e) {
            log.error("检查知识库更新失败", e);
            return false;
        }
    }

    @Override
    public String getStatus() {
        return kbManagerService.getStatus();
    }

    @Override
    public boolean clearCache() {
        try {
            log.info("收到清理知识库缓存请求");
            return kbManagerService.clearCache();
        } catch (Exception e) {
            log.error("清理知识库缓存失败", e);
            return false;
        }
    }

    @Override
    public double getMemoryUsage() {
        try {
            return kbManagerService.getMemoryUsage();
        } catch (Exception e) {
            log.error("获取内存使用情况失败", e);
            return 0.0;
        }
    }

    /**
     * 从统计信息中获取整数值
     */
    private Integer getIntValue(Map<String, Object> stats, String key) {
        Object value = stats.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        switch (status) {
            case "LOADING":
                return "知识库正在加载中...";
            case "READY":
                return "知识库已就绪，可以正常使用";
            case "ERROR":
                return "知识库加载失败，请检查配置或重新加载";
            case "CLEARED":
                return "知识库缓存已清理";
            default:
                return "未知状态: " + status;
        }
    }
}
