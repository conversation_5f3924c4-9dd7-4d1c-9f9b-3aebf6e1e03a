package cn.savas.hub.module.algocell.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 匹配结果领域对象
 *
 * 表示单个指标的匹配结果，包含匹配率、得分、缺失要素和证据信息
 * 用于支持自动绑定和人工确认的决策流程
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 指标ID
     */
    private Long indicatorId;

    /**
     * 指标代码
     */
    private String indicatorCode;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 匹配率
     * 范围：0.0-1.0，表示匹配的要素数量占总要求要素数量的比例
     * 1.0表示100%匹配，可以自动绑定
     */
    private Double matchRate;

    /**
     * 细粒度得分
     * 综合考虑匹配模式、权重、置信度等因素的详细得分
     * 用于相同匹配率情况下的排序
     */
    private Double score;

    /**
     * 缺失要素列表
     * 记录未匹配到的要素要求，格式：featureCode=valueCode 或 featureCode(ANY)
     */
    @Builder.Default
    private List<String> missingFeatures = new ArrayList<>();

    /**
     * 匹配证据列表
     * 记录所有相关的匹配证据，用于可解释性分析
     */
    @Builder.Default
    private List<Evidence> evidences = new ArrayList<>();

    /**
     * 匹配的要素数量
     * 成功匹配的要素要求数量
     */
    private Integer matchedCount;

    /**
     * 总要素数量
     * 指标要求的总要素数量
     */
    private Integer totalCount;

    /**
     * 冲突信息
     * 记录检测到的要素值冲突情况
     */
    private String conflictInfo;

    /**
     * 判断是否为完全匹配
     *
     * @return 如果匹配率为1.0则返回true
     */
    public boolean isFullMatch() {
        return matchRate != null && matchRate >= 1.0;
    }

    /**
     * 判断是否为高质量匹配
     *
     * @return 如果匹配率>=0.8则返回true
     */
    public boolean isHighQualityMatch() {
        return matchRate != null && matchRate >= 0.8;
    }

    /**
     * 判断是否可以自动绑定
     *
     * @return 如果是完全匹配且没有冲突则返回true
     */
    public boolean canAutoBinding() {
        return isFullMatch() && (conflictInfo == null || conflictInfo.trim().isEmpty());
    }

    /**
     * 获取安全的匹配率
     *
     * @return 如果匹配率为null则返回0.0
     */
    public double getSafeMatchRate() {
        return matchRate != null ? matchRate : 0.0;
    }

    /**
     * 获取安全的得分
     *
     * @return 如果得分为null则返回0.0
     */
    public double getSafeScore() {
        return score != null ? score : 0.0;
    }

    /**
     * 添加缺失要素
     *
     * @param missingFeature 缺失的要素描述
     */
    public void addMissingFeature(String missingFeature) {
        if (missingFeatures == null) {
            missingFeatures = new ArrayList<>();
        }
        missingFeatures.add(missingFeature);
    }

    /**
     * 添加匹配证据
     *
     * @param evidence 匹配证据
     */
    public void addEvidence(Evidence evidence) {
        if (evidences == null) {
            evidences = new ArrayList<>();
        }
        evidences.add(evidence);
    }

    /**
     * 获取证据数量
     *
     * @return 证据列表的大小
     */
    public int getEvidenceCount() {
        return evidences != null ? evidences.size() : 0;
    }

    /**
     * 要素匹配信息内部类
     */
    public static class FeatureMatchInfo {
        private final boolean matched;
        private final double score;

        public FeatureMatchInfo(boolean matched, double score) {
            this.matched = matched;
            this.score = score;
        }

        public boolean isMatched() {
            return matched;
        }

        public double getScore() {
            return score;
        }
    }
}
