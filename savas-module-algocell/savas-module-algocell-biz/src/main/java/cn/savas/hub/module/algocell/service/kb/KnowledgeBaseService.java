package cn.savas.hub.module.algocell.service.kb;

import cn.savas.hub.module.algocell.controller.admin.knowledge.vo.KbVersionRespVO;

/**
 * 知识库管理服务接口
 *
 * 提供知识库版本管理、热更新等功能
 *
 * <AUTHOR>
 */
public interface KnowledgeBaseService {

    /**
     * 获取知识库版本信息
     *
     * 返回当前知识库的版本、统计信息和状态
     *
     * @return 知识库版本信息
     */
    KbVersionRespVO getVersion();

    /**
     * 重新加载知识库
     *
     * 从数据库重新加载知识库数据，用于热更新
     *
     * @return 重新加载是否成功
     */
    boolean reload();

    /**
     * 检查知识库更新
     *
     * 检查数据库中的知识库版本是否有更新
     *
     * @return 如果有更新返回true
     */
    boolean checkForUpdates();

    /**
     * 获取知识库状态
     *
     * 返回知识库的当前状态：LOADING、READY、ERROR
     *
     * @return 状态字符串
     */
    String getStatus();

    /**
     * 清理知识库缓存
     *
     * 清理内存中的知识库缓存数据
     *
     * @return 清理是否成功
     */
    boolean clearCache();

    /**
     * 获取知识库内存使用情况
     *
     * 返回知识库在内存中的使用情况（MB）
     *
     * @return 内存使用量
     */
    double getMemoryUsage();
}
