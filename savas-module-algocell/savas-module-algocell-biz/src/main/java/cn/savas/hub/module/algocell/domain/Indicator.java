package cn.savas.hub.module.algocell.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 指标领域对象
 *
 * 表示业务指标，如"化工、炼油 中低压阀门DN15~DN50"
 * 每个指标都有特定的要素要求，用于匹配判断
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Indicator implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 指标ID
     * 数据库主键
     */
    private Long id;

    /**
     * 指标代码
     * 业务唯一标识，例如：2-5001
     */
    private String code;

    /**
     * 指标名称
     * 业务描述，例如：化工、炼油 中低压阀门DN15~DN50
     */
    private String name;

    /**
     * 指标描述
     * 详细说明信息
     */
    private String description;

    /**
     * 是否启用
     * 控制指标是否参与匹配
     */
    private Boolean enabled;

    /**
     * 排序权重
     * 用于相同匹配率时的排序，数值越大优先级越高
     */
    private Integer sortWeight;

    /**
     * 判断指标是否可用
     *
     * @return 如果启用且基本信息完整则返回true
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(enabled)
            && code != null && !code.trim().isEmpty()
            && name != null && !name.trim().isEmpty();
    }

    /**
     * 获取安全的排序权重
     *
     * @return 如果权重为null则返回0
     */
    public int getSafeSortWeight() {
        return sortWeight != null ? sortWeight : 0;
    }
}
