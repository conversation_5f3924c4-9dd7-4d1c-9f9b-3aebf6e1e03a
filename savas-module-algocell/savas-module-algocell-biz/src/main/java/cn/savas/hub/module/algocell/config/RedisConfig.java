//package cn.savas.hub.module.algocell.config;
//
//import com.fasterxml.jackson.annotation.JsonAutoDetect;
//import com.fasterxml.jackson.annotation.PropertyAccessor;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
//import org.springframework.data.redis.serializer.StringRedisSerializer;
//
///**
// * Redis配置类
// *
// * 为AlgoCell模块配置Redis缓存
// *
// * <AUTHOR>
// */
//@Slf4j
//@Configuration
//@ConditionalOnProperty(prefix = "savas.algocell.cache", name = "enable-redis-cache", havingValue = "true")
//public class RedisConfig {
//
//    /**
//     * 配置RedisTemplate
//     *
//     * @param connectionFactory Redis连接工厂
//     * @return 配置好的RedisTemplate
//     */
//    @Bean(name = "algoCellRedisTemplate")
//    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
//        RedisTemplate<String, Object> template = new RedisTemplate<>();
//        template.setConnectionFactory(connectionFactory);
//
//        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
//        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
//        ObjectMapper objectMapper = new ObjectMapper();
//        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
//        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
//
//        // 使用StringRedisSerializer来序列化和反序列化redis的key值
//        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
//
//        // 设置key和value的序列化规则
//        template.setKeySerializer(stringRedisSerializer);
//        template.setValueSerializer(jackson2JsonRedisSerializer);
//        template.setHashKeySerializer(stringRedisSerializer);
//        template.setHashValueSerializer(jackson2JsonRedisSerializer);
//
//        // 设置支持事务
//        template.setEnableTransactionSupport(true);
//        template.afterPropertiesSet();
//
//        log.info("AlgoCell Redis配置初始化完成");
//        return template;
//    }
//
//    /**
//     * 配置ObjectMapper用于JSON序列化
//     *
//     * @return ObjectMapper实例
//     */
//    @Bean(name = "algoCellObjectMapper")
//    public ObjectMapper objectMapper() {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
//        return mapper;
//    }
//}
