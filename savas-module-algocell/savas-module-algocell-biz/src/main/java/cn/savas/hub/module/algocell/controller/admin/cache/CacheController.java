package cn.savas.hub.module.algocell.controller.admin.cache;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.module.algocell.service.cache.MatchCacheService;
import cn.savas.hub.module.algocell.service.cache.RedisCacheService;
import cn.savas.hub.module.algocell.service.cache.RedisHealthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * 缓存管理控制器
 *
 * 提供缓存管理、Redis健康检查等API接口
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 缓存管理")
@RestController
@RequestMapping("/algocell/cache")
@Validated
@Slf4j
public class CacheController {

    @Resource
    private MatchCacheService matchCacheService;

    @Resource
    private RedisCacheService redisCacheService;

    @Resource
    private RedisHealthService redisHealthService;

    @GetMapping("/statistics")
    @Operation(summary = "获取缓存统计信息")
    public CommonResult<Map<String, Object>> getCacheStatistics() {
        Map<String, Object> statistics = matchCacheService.getCacheStatistics();
        return success(statistics);
    }

    @PostMapping("/clear")
    @Operation(summary = "清理所有缓存")
    public CommonResult<Boolean> clearAllCache() {
        log.info("收到清理所有缓存请求");

        try {
            matchCacheService.clearAll();
            log.info("所有缓存清理完成");
            return success(true);
        } catch (Exception e) {
            log.error("清理缓存失败", e);
            return success(false);
        }
    }

    @PostMapping("/clear/local")
    @Operation(summary = "清理本地缓存")
    public CommonResult<Boolean> clearLocalCache() {
        log.info("收到清理本地缓存请求");

        try {
            // 这里可以添加只清理本地缓存的逻辑
            matchCacheService.clearAll();
            log.info("本地缓存清理完成");
            return success(true);
        } catch (Exception e) {
            log.error("清理本地缓存失败", e);
            return success(false);
        }
    }

    @PostMapping("/clear/redis")
    @Operation(summary = "清理Redis缓存")
    public CommonResult<Boolean> clearRedisCache() {
        log.info("收到清理Redis缓存请求");

        try {
            redisCacheService.clearKbCache();
            log.info("Redis缓存清理完成");
            return success(true);
        } catch (Exception e) {
            log.error("清理Redis缓存失败", e);
            return success(false);
        }
    }

    @GetMapping("/redis/health")
    @Operation(summary = "获取Redis健康状态")
    public CommonResult<RedisHealthService.RedisHealthInfo> getRedisHealth() {
        RedisHealthService.RedisHealthInfo healthInfo = redisHealthService.getHealthInfo();
        return success(healthInfo);
    }

    @PostMapping("/redis/health/check")
    @Operation(summary = "手动触发Redis健康检查")
    public CommonResult<Boolean> checkRedisHealth() {
        log.info("收到手动Redis健康检查请求");

        try {
            redisHealthService.checkRedisHealth();
            log.info("Redis健康检查完成");
            return success(true);
        } catch (Exception e) {
            log.error("Redis健康检查失败", e);
            return success(false);
        }
    }

    @PostMapping("/redis/enable")
    @Operation(summary = "启用Redis缓存")
    public CommonResult<Boolean> enableRedis() {
        log.info("收到启用Redis缓存请求");

        try {
            redisHealthService.setRedisAvailable(true);
            log.info("Redis缓存已启用");
            return success(true);
        } catch (Exception e) {
            log.error("启用Redis缓存失败", e);
            return success(false);
        }
    }

    @PostMapping("/redis/disable")
    @Operation(summary = "禁用Redis缓存")
    public CommonResult<Boolean> disableRedis() {
        log.info("收到禁用Redis缓存请求");

        try {
            redisHealthService.setRedisAvailable(false);
            log.info("Redis缓存已禁用");
            return success(true);
        } catch (Exception e) {
            log.error("禁用Redis缓存失败", e);
            return success(false);
        }
    }

    @GetMapping("/redis/available")
    @Operation(summary = "检查Redis是否可用")
    public CommonResult<Boolean> isRedisAvailable() {
        boolean available = redisCacheService.isAvailable();
        return success(available);
    }
}
