package cn.savas.hub.module.algocell.dal.dataobject.kb;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 指标要素要求表 DO
 *
 * 对应数据库表 indicator_feature_req
 * 存储指标对特定要素的要求配置
 *
 * <AUTHOR>
 */
@TableName("algocell_indicator_feature_req")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorFeatureReqDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 指标ID
     * 关联 indicator.id
     */
    private Long indicatorId;

    /**
     * 要素代码
     * 关联 kb_feature.code
     */
    private String featureCode;

    /**
     * 要求的要素值代码
     * 关联 kb_value.code，如果为NULL表示仅要求该要素存在
     */
    private String requiredValueCode;

    /**
     * 匹配模式
     * EXACT：精确匹配
     * ANCESTOR_OK：祖先匹配（命中子值也算匹配）
     * DESCENDANT_OK：后代匹配（命中父值也算匹配）
     * ANY：任意匹配（该要素出现即可）
     */
    private String matchMode;

    /**
     * 要素权重
     * 用于计算匹配得分，默认为1.0
     */
    private Double weight;

    /**
     * 是否必需
     * 标识该要素是否为必需项
     */
    private Boolean required;

    /**
     * 排序序号
     * 用于要素显示排序
     */
    private Integer sortOrder;

    /**
     * 备注信息
     */
    private String remark;
}
