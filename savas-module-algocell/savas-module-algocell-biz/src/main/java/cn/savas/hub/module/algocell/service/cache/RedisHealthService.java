package cn.savas.hub.module.algocell.service.cache;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis健康检查和降级服务
 * 
 * 监控Redis连接状态，实现自动降级和恢复机制
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisHealthService {
    
    @Resource
    private AlgoCellProperties properties;
    
    @Resource(name = "algoCellRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * Redis可用状态
     */
    private final AtomicBoolean redisAvailable = new AtomicBoolean(false);
    
    /**
     * 连续失败次数
     */
    private final AtomicLong consecutiveFailures = new AtomicLong(0);
    
    /**
     * 最大连续失败次数（超过后进入降级模式）
     */
    private static final long MAX_CONSECUTIVE_FAILURES = 3;
    
    /**
     * 健康检查键
     */
    private static final String HEALTH_CHECK_KEY = "KB:HEALTH_CHECK";
    
    @PostConstruct
    public void init() {
        if (!isRedisEnabled()) {
            log.info("Redis功能已禁用，跳过健康检查");
            return;
        }
        
        // 初始健康检查
        checkRedisHealth();
        log.info("Redis健康检查服务已启动");
    }
    
    /**
     * 定期健康检查
     * 每30秒检查一次Redis连接状态
     */
    @Scheduled(fixedRate = 30000)
    public void scheduledHealthCheck() {
        if (!isRedisEnabled()) {
            return;
        }
        
        checkRedisHealth();
    }
    
    /**
     * 检查Redis健康状态
     */
    public void checkRedisHealth() {
        try {
            // 执行简单的ping操作
            String testKey = HEALTH_CHECK_KEY + ":" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(testKey, "OK", 10, TimeUnit.SECONDS);
            String result = (String) redisTemplate.opsForValue().get(testKey);
            
            if ("OK".equals(result)) {
                // 健康检查成功
                if (!redisAvailable.get()) {
                    log.info("Redis连接已恢复");
                    redisAvailable.set(true);
                }
                consecutiveFailures.set(0);
                
                // 清理测试键
                redisTemplate.delete(testKey);
            } else {
                handleRedisFailure("健康检查返回值异常: " + result);
            }
            
        } catch (Exception e) {
            handleRedisFailure("健康检查异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理Redis失败
     * 
     * @param errorMessage 错误信息
     */
    private void handleRedisFailure(String errorMessage) {
        long failures = consecutiveFailures.incrementAndGet();
        
        if (failures >= MAX_CONSECUTIVE_FAILURES) {
            if (redisAvailable.get()) {
                log.warn("Redis连续失败{}次，进入降级模式: {}", failures, errorMessage);
                redisAvailable.set(false);
            }
        } else {
            log.debug("Redis健康检查失败({}/{}): {}", failures, MAX_CONSECUTIVE_FAILURES, errorMessage);
        }
    }
    
    /**
     * 判断Redis是否可用
     * 
     * @return Redis可用状态
     */
    public boolean isRedisAvailable() {
        return isRedisEnabled() && redisAvailable.get();
    }
    
    /**
     * 强制设置Redis可用状态
     * 
     * @param available 可用状态
     */
    public void setRedisAvailable(boolean available) {
        if (available != redisAvailable.get()) {
            log.info("手动设置Redis状态: {}", available ? "可用" : "不可用");
            redisAvailable.set(available);
            if (available) {
                consecutiveFailures.set(0);
            }
        }
    }
    
    /**
     * 获取连续失败次数
     * 
     * @return 连续失败次数
     */
    public long getConsecutiveFailures() {
        return consecutiveFailures.get();
    }
    
    /**
     * 获取Redis健康状态信息
     * 
     * @return 健康状态信息
     */
    public RedisHealthInfo getHealthInfo() {
        RedisHealthInfo info = new RedisHealthInfo();
        info.enabled = isRedisEnabled();
        info.available = redisAvailable.get();
        info.consecutiveFailures = consecutiveFailures.get();
        info.maxConsecutiveFailures = MAX_CONSECUTIVE_FAILURES;
        info.degraded = info.consecutiveFailures >= MAX_CONSECUTIVE_FAILURES;
        
        return info;
    }
    
    /**
     * 判断Redis功能是否启用
     */
    private boolean isRedisEnabled() {
        return properties.getCache().getEnabled() && 
               properties.getCache().getEnableRedisCache();
    }
    
    /**
     * Redis健康信息类
     */
    public static class RedisHealthInfo {
        public boolean enabled;
        public boolean available;
        public long consecutiveFailures;
        public long maxConsecutiveFailures;
        public boolean degraded;
        
        @Override
        public String toString() {
            return String.format("RedisHealth{enabled=%s, available=%s, failures=%d/%d, degraded=%s}",
                enabled, available, consecutiveFailures, maxConsecutiveFailures, degraded);
        }
    }
}
