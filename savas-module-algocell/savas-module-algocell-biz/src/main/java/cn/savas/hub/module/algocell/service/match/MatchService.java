package cn.savas.hub.module.algocell.service.match;

import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchBatchReqVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchResultRespVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchRowReqVO;

import java.util.List;
import java.util.Map;

/**
 * 知识库匹配服务接口
 *
 * 提供行级匹配、批量匹配等核心功能
 *
 * <AUTHOR>
 */
public interface MatchService {

    /**
     * 单行数据匹配
     *
     * 对单行表格数据进行知识库匹配，返回候选指标列表
     *
     * @param request 匹配请求参数
     * @return 匹配结果响应
     */
    MatchResultRespVO matchRow(MatchRowReqVO request);

    /**
     * 批量数据匹配
     *
     * 对多行数据进行批量匹配处理
     *
     * @param request 批量匹配请求参数
     * @return 批量匹配结果列表
     */
    List<MatchResultRespVO> matchBatch(MatchBatchReqVO request);

    /**
     * 原始行数据匹配（内部接口）
     *
     * 直接使用Map格式的行数据进行匹配，返回原始匹配结果
     * 主要用于内部调用和测试
     *
     * @param rowData 行数据，键为列名，值为列值
     * @param threshold 匹配阈值，只返回匹配率大于等于该值的结果
     * @param maxResults 最大返回结果数
     * @return 匹配结果列表，按匹配率和得分排序
     */
    List<MatchResult> matchRowData(Map<String, String> rowData, double threshold, int maxResults);

    /**
     * 获取指标的详细要素要求
     *
     * 返回指定指标的所有要素要求信息，用于调试和分析
     *
     * @param indicatorId 指标ID
     * @return 要素要求描述列表
     */
    List<String> getIndicatorRequirements(Long indicatorId);

    /**
     * 预热匹配引擎
     * 预加载知识库数据到内存，提升首次匹配的性能
     *
     * @return 预热是否成功
     */
    boolean warmUp();

    /**
     * 获取匹配统计信息
     * 返回匹配引擎的运行统计信息
     *
     * @return 统计信息Map，包含匹配次数、平均耗时等
     */
    Map<String, Object> getMatchStatistics();
}
