package cn.savas.hub.module.algocell.service.cache;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存服务
 *
 * 根据matching.md第2.2节要求实现Redis缓存功能：
 * - KB:TRIE：字符串术语Trie的序列化
 * - KB:REGEX_LIST：正则表达式列表的序列化
 * - KB:TERM2VAL：术语到要素值的映射
 * - KB:VAL2IND：要素值到指标的倒排索引
 * - KB:FEATURE_WEIGHT：要素权重表
 * - KB:VERSION：知识库版本号
 *
 * 使用框架提供的Redis配置，无需额外配置
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisCacheService {

    @Resource
    private AlgoCellProperties properties;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RedisHealthService redisHealthService;

    /**
     * Redis键名常量
     */
    private static final String KEY_TRIE = "TRIE";
    private static final String KEY_REGEX_LIST = "REGEX_LIST";
    private static final String KEY_TERM2VAL = "TERM2VAL";
    private static final String KEY_VAL2IND = "VAL2IND";
    private static final String KEY_FEATURE_WEIGHT = "FEATURE_WEIGHT";
    private static final String KEY_VERSION = "VERSION";
    private static final String KEY_MATCH_RESULT = "MATCH_RESULT";
    private static final String KEY_NORMALIZED_TEXT = "NORMALIZED_TEXT";

    /**
     * 是否可用标志
     */
    private volatile boolean available = false;

    @PostConstruct
    public void init() {
        if (!isRedisEnabled()) {
            log.info("Redis缓存功能已禁用");
            return;
        }

        try {
            // 测试Redis连接
            redisTemplate.opsForValue().set(buildKey("TEST"), "OK", 10, TimeUnit.SECONDS);
            String result = (String) redisTemplate.opsForValue().get(buildKey("TEST"));
            if ("OK".equals(result)) {
                available = true;
                log.info("Redis缓存服务初始化成功");
            } else {
                log.warn("Redis连接测试失败，将使用本地缓存");
            }
        } catch (Exception e) {
            log.warn("Redis缓存服务初始化失败，将使用本地缓存: {}", e.getMessage());
            available = false;
        }
    }

    /**
     * 缓存知识库版本
     *
     * @param version 版本号
     */
    public void cacheKbVersion(String version) {
        if (!isAvailable()) {
            return;
        }

        try {
            String key = buildKey(KEY_VERSION);
            redisTemplate.opsForValue().set(key, version, getExpireHours(), TimeUnit.HOURS);
            log.debug("缓存知识库版本: {}", version);
        } catch (Exception e) {
            log.warn("缓存知识库版本失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的知识库版本
     *
     * @return 版本号，如果不存在则返回null
     */
    public String getCachedKbVersion() {
        if (!isAvailable()) {
            return null;
        }

        try {
            String key = buildKey(KEY_VERSION);
            return (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.warn("获取缓存知识库版本失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 缓存要素权重表
     *
     * @param featureWeights 要素权重映射
     */
    public void cacheFeatureWeights(Map<String, Double> featureWeights) {
        if (!isAvailable() || featureWeights == null) {
            return;
        }

        try {
            String key = buildKey(KEY_FEATURE_WEIGHT);
            String json = objectMapper.writeValueAsString(featureWeights);
            redisTemplate.opsForValue().set(key, json, getExpireHours(), TimeUnit.HOURS);
            log.debug("缓存要素权重表，数量: {}", featureWeights.size());
        } catch (Exception e) {
            log.warn("缓存要素权重表失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的要素权重表
     *
     * @return 要素权重映射，如果不存在则返回null
     */
    public Map<String, Double> getCachedFeatureWeights() {
        if (!isAvailable()) {
            return null;
        }

        try {
            String key = buildKey(KEY_FEATURE_WEIGHT);
            String json = (String) redisTemplate.opsForValue().get(key);
            if (json != null) {
                return objectMapper.readValue(json, new TypeReference<Map<String, Double>>() {});
            }
        } catch (Exception e) {
            log.warn("获取缓存要素权重表失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 缓存要素值到指标的倒排索引
     *
     * @param featureValueKey 要素值键
     * @param indicatorIds 指标ID集合
     */
    public void cacheValueToIndicators(String featureValueKey, Set<Long> indicatorIds) {
        if (!isAvailable() || featureValueKey == null || indicatorIds == null) {
            return;
        }

        try {
            String key = buildKey(KEY_VAL2IND + ":" + featureValueKey);
            String json = objectMapper.writeValueAsString(indicatorIds);
            redisTemplate.opsForValue().set(key, json, getExpireHours(), TimeUnit.HOURS);
            log.debug("缓存倒排索引: {} -> {} 个指标", featureValueKey, indicatorIds.size());
        } catch (Exception e) {
            log.warn("缓存倒排索引失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的要素值到指标的倒排索引
     *
     * @param featureValueKey 要素值键
     * @return 指标ID集合，如果不存在则返回null
     */
    public Set<Long> getCachedValueToIndicators(String featureValueKey) {
        if (!isAvailable() || featureValueKey == null) {
            return null;
        }

        try {
            String key = buildKey(KEY_VAL2IND + ":" + featureValueKey);
            String json = (String) redisTemplate.opsForValue().get(key);
            if (json != null) {
                return objectMapper.readValue(json, new TypeReference<Set<Long>>() {});
            }
        } catch (Exception e) {
            log.warn("获取缓存倒排索引失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 缓存术语到要素值的映射
     *
     * @param termHash 术语哈希
     * @param termMappings 术语映射
     */
    public void cacheTermToValueMappings(String termHash, Map<String, List<String>> termMappings) {
        if (!isAvailable() || termHash == null || termMappings == null) {
            return;
        }

        try {
            String key = buildKey(KEY_TERM2VAL + ":" + termHash);
            String json = objectMapper.writeValueAsString(termMappings);
            redisTemplate.opsForValue().set(key, json, getExpireHours(), TimeUnit.HOURS);
            log.debug("缓存术语映射: {} -> {} 个术语", termHash, termMappings.size());
        } catch (Exception e) {
            log.warn("缓存术语映射失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的术语到要素值的映射
     *
     * @param termHash 术语哈希
     * @return 术语映射，如果不存在则返回null
     */
    public Map<String, List<String>> getCachedTermToValueMappings(String termHash) {
        if (!isAvailable() || termHash == null) {
            return null;
        }

        try {
            String key = buildKey(KEY_TERM2VAL + ":" + termHash);
            String json = (String) redisTemplate.opsForValue().get(key);
            if (json != null) {
                return objectMapper.readValue(json, new TypeReference<Map<String, List<String>>>() {});
            }
        } catch (Exception e) {
            log.warn("获取缓存术语映射失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 清理所有知识库相关的缓存
     */
    public void clearKbCache() {
        if (!isAvailable()) {
            return;
        }

        try {
            String pattern = buildKey("*");
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清理Redis知识库缓存，删除键数量: {}", keys.size());
            }
        } catch (Exception e) {
            log.warn("清理Redis知识库缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 缓存匹配结果
     *
     * @param cacheKey 缓存键
     * @param results 匹配结果列表
     */
    public void cacheMatchResults(String cacheKey, List<?> results) {
        if (!isAvailable() || cacheKey == null || results == null) {
            return;
        }

        try {
            String key = buildKey(KEY_MATCH_RESULT + ":" + cacheKey);
            String json = objectMapper.writeValueAsString(results);
            redisTemplate.opsForValue().set(key, json, getExpireHours(), TimeUnit.HOURS);
            log.debug("缓存匹配结果: {} -> {} 个结果", cacheKey, results.size());
        } catch (Exception e) {
            log.warn("缓存匹配结果失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的匹配结果
     *
     * @param cacheKey 缓存键
     * @param typeReference 类型引用
     * @return 匹配结果列表，如果不存在则返回null
     */
    public <T> T getCachedMatchResults(String cacheKey, TypeReference<T> typeReference) {
        if (!isAvailable() || cacheKey == null) {
            return null;
        }

        try {
            String key = buildKey(KEY_MATCH_RESULT + ":" + cacheKey);
            String json = (String) redisTemplate.opsForValue().get(key);
            if (json != null) {
                return objectMapper.readValue(json, typeReference);
            }
        } catch (Exception e) {
            log.warn("获取缓存匹配结果失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 缓存标准化文本
     *
     * @param originalText 原始文本
     * @param normalizedText 标准化文本
     */
    public void cacheNormalizedText(String originalText, String normalizedText) {
        if (!isAvailable() || originalText == null || normalizedText == null) {
            return;
        }

        try {
            String key = buildKey(KEY_NORMALIZED_TEXT + ":" + originalText.hashCode());
            redisTemplate.opsForValue().set(key, normalizedText, getExpireHours(), TimeUnit.HOURS);
            log.debug("缓存标准化文本: {} -> {}", originalText, normalizedText);
        } catch (Exception e) {
            log.warn("缓存标准化文本失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的标准化文本
     *
     * @param originalText 原始文本
     * @return 标准化文本，如果不存在则返回null
     */
    public String getCachedNormalizedText(String originalText) {
        if (!isAvailable() || originalText == null) {
            return null;
        }

        try {
            String key = buildKey(KEY_NORMALIZED_TEXT + ":" + originalText.hashCode());
            return (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.warn("获取缓存标准化文本失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查知识库版本是否有更新
     *
     * @param currentVersion 当前版本
     * @return 如果缓存中的版本更新则返回true
     */
    public boolean hasKbVersionUpdate(String currentVersion) {
        String cachedVersion = getCachedKbVersion();
        return cachedVersion != null && !cachedVersion.equals(currentVersion);
    }

    /**
     * 构建Redis键名
     *
     * @param suffix 键后缀
     * @return 完整的Redis键名
     */
    private String buildKey(String suffix) {
        return properties.getCache().getRedisKeyPrefix() + suffix;
    }

    /**
     * 判断Redis缓存是否启用
     */
    private boolean isRedisEnabled() {
        return properties.getCache().getEnabled() &&
               properties.getCache().getEnableRedisCache();
    }

    /**
     * 判断Redis服务是否可用
     */
    public boolean isAvailable() {
        return isRedisEnabled() && available && redisHealthService.isRedisAvailable();
    }

    /**
     * 获取过期时间（小时）
     */
    private int getExpireHours() {
        return properties.getCache().getRedisExpireHours();
    }
}
