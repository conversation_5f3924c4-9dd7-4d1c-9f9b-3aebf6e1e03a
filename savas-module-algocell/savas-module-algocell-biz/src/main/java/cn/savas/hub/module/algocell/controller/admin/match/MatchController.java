package cn.savas.hub.module.algocell.controller.admin.match;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.module.algocell.service.match.MatchService;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchBatchReqVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchResultRespVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchRowReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * 知识库匹配控制器
 *
 * 提供行级匹配、批量匹配等API接口
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 知识库匹配")
@RestController
@RequestMapping("/algocell/match")
@Validated
@Slf4j
public class MatchController {

    @Resource
    private MatchService matchService;

    @PostMapping("/row")
    @Operation(summary = "单行数据匹配")
    public CommonResult<MatchResultRespVO> matchRow(@Valid @RequestBody MatchRowReqVO request) {
        log.info("收到单行匹配请求，列数: {}", request.getRowData().size());

        MatchResultRespVO result = matchService.matchRow(request);

        log.info("单行匹配完成，自动绑定: {}个，候选: {}个",
                result.getAutoBindingCount(), result.getCandidateCount());

        return success(result);
    }

    @PostMapping("/batch")
    @Operation(summary = "批量数据匹配")
    public CommonResult<List<MatchResultRespVO>> matchBatch(@Valid @RequestBody MatchBatchReqVO request) {
        log.info("收到批量匹配请求，行数: {}", request.getRowCount());

        List<MatchResultRespVO> results = matchService.matchBatch(request);

        log.info("批量匹配完成，处理行数: {}", results.size());

        return success(results);
    }

    @GetMapping("/indicator/{indicatorId}/requirements")
    @Operation(summary = "获取指标要素要求")
    @Parameter(name = "indicatorId", description = "指标ID", required = true)
    public CommonResult<List<String>> getIndicatorRequirements(@PathVariable("indicatorId") Long indicatorId) {
        List<String> requirements = matchService.getIndicatorRequirements(indicatorId);
        return success(requirements);
    }

    @PostMapping("/warmup")
    @Operation(summary = "预热匹配引擎")
    public CommonResult<Boolean> warmUp() {
        log.info("收到预热匹配引擎请求");

        boolean success = matchService.warmUp();

        log.info("匹配引擎预热{}", success ? "成功" : "失败");

        return success(success);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取匹配统计信息")
    public CommonResult<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = matchService.getMatchStatistics();
        return success(statistics);
    }

    @PostMapping("/test")
    @Operation(summary = "测试匹配接口")
    public CommonResult<MatchResultRespVO> testMatch() {
        // 创建测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("standard", "闸阀,CL150,A105/13Cr*STL,BB,OS&Y,RF,API 602||英吋直径:1||管表号/压力等级:CL150");
        MatchRowReqVO testRequest = MatchRowReqVO.builder()
            .rowData(rowData)
            .threshold(0.0)
            .maxResults(10)
            .includeEvidence(true)
            .enableConflictDetection(true)
            .businessId("TEST")
            .build();

        log.info("执行测试匹配");

        MatchResultRespVO result = matchService.matchRow(testRequest);

        log.info("测试匹配完成，结果数: {}", result.getTotalMatches());

        return success(result);
    }
}
