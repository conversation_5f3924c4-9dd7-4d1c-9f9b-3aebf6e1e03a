package cn.savas.hub.module.algocell.service.monitor;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控服务
 * 
 * 监控匹配性能、统计慢查询、记录系统指标
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class PerformanceMonitorService {
    
    @Resource
    private AlgoCellProperties properties;
    
    /**
     * 性能统计数据
     */
    private final PerformanceStats stats = new PerformanceStats();
    
    /**
     * 慢查询记录
     */
    private final Map<String, SlowQueryRecord> slowQueries = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        if (properties.getPerformance().getEnableMonitoring()) {
            log.info("性能监控服务已启用");
        } else {
            log.info("性能监控服务已禁用");
        }
    }
    
    /**
     * 记录匹配操作
     * 
     * @param operation 操作类型
     * @param duration 执行时间（毫秒）
     * @param success 是否成功
     */
    public void recordMatch(String operation, long duration, boolean success) {
        if (!isMonitoringEnabled()) {
            return;
        }
        
        stats.totalRequests.increment();
        stats.totalDuration.add(duration);
        
        if (success) {
            stats.successRequests.increment();
        } else {
            stats.failedRequests.increment();
        }
        
        // 更新最大和最小执行时间
        updateMinMaxDuration(duration);
        
        // 检查是否为慢查询
        if (duration > properties.getPerformance().getSlowQueryThreshold()) {
            recordSlowQuery(operation, duration);
        }
        
        log.debug("记录匹配操作: operation={}, duration={}ms, success={}", operation, duration, success);
    }
    
    /**
     * 记录慢查询
     */
    private void recordSlowQuery(String operation, long duration) {
        String key = operation + "_" + System.currentTimeMillis();
        SlowQueryRecord record = new SlowQueryRecord();
        record.operation = operation;
        record.duration = duration;
        record.timestamp = LocalDateTime.now();
        
        slowQueries.put(key, record);
        
        // 限制慢查询记录数量，避免内存泄漏
        if (slowQueries.size() > 1000) {
            // 移除最旧的记录
            String oldestKey = slowQueries.keySet().iterator().next();
            slowQueries.remove(oldestKey);
        }
        
        log.warn("检测到慢查询: operation={}, duration={}ms", operation, duration);
    }
    
    /**
     * 更新最大最小执行时间
     */
    private void updateMinMaxDuration(long duration) {
        // 更新最大值
        long currentMax = stats.maxDuration.get();
        while (duration > currentMax) {
            if (stats.maxDuration.compareAndSet(currentMax, duration)) {
                break;
            }
            currentMax = stats.maxDuration.get();
        }
        
        // 更新最小值
        long currentMin = stats.minDuration.get();
        if (currentMin == 0 || duration < currentMin) {
            while (currentMin == 0 || duration < currentMin) {
                if (stats.minDuration.compareAndSet(currentMin, duration)) {
                    break;
                }
                currentMin = stats.minDuration.get();
            }
        }
    }
    
    /**
     * 获取性能统计信息
     * 
     * @return 统计信息Map
     */
    public Map<String, Object> getPerformanceStatistics() {
        Map<String, Object> result = new ConcurrentHashMap<>();
        
        if (!isMonitoringEnabled()) {
            result.put("monitoring_enabled", false);
            return result;
        }
        
        result.put("monitoring_enabled", true);
        result.put("total_requests", stats.totalRequests.sum());
        result.put("success_requests", stats.successRequests.sum());
        result.put("failed_requests", stats.failedRequests.sum());
        result.put("total_duration_ms", stats.totalDuration.sum());
        result.put("max_duration_ms", stats.maxDuration.get());
        result.put("min_duration_ms", stats.minDuration.get());
        
        // 计算平均执行时间
        long totalRequests = stats.totalRequests.sum();
        if (totalRequests > 0) {
            result.put("average_duration_ms", stats.totalDuration.sum() / (double) totalRequests);
            result.put("success_rate", stats.successRequests.sum() / (double) totalRequests);
        } else {
            result.put("average_duration_ms", 0.0);
            result.put("success_rate", 0.0);
        }
        
        result.put("slow_query_count", slowQueries.size());
        result.put("slow_query_threshold_ms", properties.getPerformance().getSlowQueryThreshold());
        
        return result;
    }
    
    /**
     * 获取慢查询记录
     * 
     * @return 慢查询记录Map
     */
    public Map<String, SlowQueryRecord> getSlowQueries() {
        return new ConcurrentHashMap<>(slowQueries);
    }
    
    /**
     * 清理统计数据
     */
    public void clearStatistics() {
        stats.reset();
        slowQueries.clear();
        log.info("性能统计数据已清理");
    }
    
    /**
     * 判断监控是否启用
     */
    private boolean isMonitoringEnabled() {
        return properties.getPerformance().getEnableMonitoring();
    }
    
    /**
     * 性能统计数据类
     */
    @Data
    private static class PerformanceStats {
        private final LongAdder totalRequests = new LongAdder();
        private final LongAdder successRequests = new LongAdder();
        private final LongAdder failedRequests = new LongAdder();
        private final LongAdder totalDuration = new LongAdder();
        private final AtomicLong maxDuration = new AtomicLong(0);
        private final AtomicLong minDuration = new AtomicLong(0);
        
        public void reset() {
            totalRequests.reset();
            successRequests.reset();
            failedRequests.reset();
            totalDuration.reset();
            maxDuration.set(0);
            minDuration.set(0);
        }
    }
    
    /**
     * 慢查询记录类
     */
    @Data
    public static class SlowQueryRecord {
        private String operation;
        private long duration;
        private LocalDateTime timestamp;
    }
}
