package cn.savas.hub.module.algocell.dal.dataobject.kb;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 知识库术语表 DO
 *
 * 对应数据库表 kb_term
 * 存储术语信息，支持字符串和正则表达式两种类型
 *
 * <AUTHOR>
 */
@TableName("algocell_kb_term")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbTermDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 要素代码
     */
    private String featureCode;

    /**
     * 要素值代码
     * 命中该术语时映射到的要素值
     */
    private String valueCode;

    /**
     * 术语类型
     * STRING：字符串匹配
     * REGEX：正则表达式匹配
     */
    private String termType;

    /**
     * 表达式内容
     * 对于STRING类型：具体的词汇或短语
     * 对于REGEX类型：正则表达式
     */
    private String expr;

    /**
     * 正则标志位
     * 用于Pattern.compile的flags参数
     */
    private Integer flags;

    /**
     * 优先级
     * 数值越大优先级越高，用于冲突解决
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 备注信息
     */
    private String remark;
}
