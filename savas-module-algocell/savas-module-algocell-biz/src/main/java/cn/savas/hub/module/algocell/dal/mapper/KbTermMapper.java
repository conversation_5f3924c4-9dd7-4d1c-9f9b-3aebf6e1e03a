package cn.savas.hub.module.algocell.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.algocell.dal.dataobject.kb.KbTermDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库术语 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KbTermMapper extends BaseMapperX<KbTermDO> {

    /**
     * 查询所有启用的术语
     *
     * @return 启用的术语列表
     */
    default List<KbTermDO> selectEnabledTerms() {
        return selectList(KbTermDO::getEnabled, true);
    }

    /**
     * 根据术语类型查询术语
     *
     * @param termType 术语类型（STRING/REGEX）
     * @return 术语列表
     */
    default List<KbTermDO> selectByTermType(String termType) {
        return selectList(new LambdaQueryWrapper<>(KbTermDO.class)
            .eq(KbTermDO::getTermType, termType)
            .eq(KbTermDO::getEnabled, true)
            .orderByDesc(KbTermDO::getPriority, KbTermDO::getId));
    }

    /**
     * 根据要素代码查询术语
     *
     * @param featureCode 要素代码
     * @return 术语列表
     */
    default List<KbTermDO> selectByFeatureCode(String featureCode) {
        return selectList(new LambdaQueryWrapper<>(KbTermDO.class)
            .eq(KbTermDO::getFeatureCode, featureCode)
            .eq(KbTermDO::getEnabled, true)
            .orderByDesc(KbTermDO::getPriority, KbTermDO::getId));
    }

    /**
     * 根据要素代码和值代码查询术语
     *
     * @param featureCode 要素代码
     * @param valueCode 值代码
     * @return 术语列表
     */
    default List<KbTermDO> selectByFeatureAndValueCode(String featureCode, String valueCode) {
        return selectList(new LambdaQueryWrapper<>(KbTermDO.class)
            .eq(KbTermDO::getFeatureCode, featureCode)
            .eq(KbTermDO::getValueCode, valueCode)
            .eq(KbTermDO::getEnabled, true)
            .orderByDesc(KbTermDO::getPriority, KbTermDO::getId));
    }

    /**
     * 查询字符串类型的术语
     *
     * @return 字符串术语列表
     */
    default List<KbTermDO> selectStringTerms() {
        return selectByTermType("STRING");
    }

    /**
     * 查询正则表达式类型的术语
     *
     * @return 正则术语列表
     */
    default List<KbTermDO> selectRegexTerms() {
        return selectByTermType("REGEX");
    }

    /**
     * 查询所有术语，按优先级排序
     *
     * @return 排序后的术语列表
     */
    default List<KbTermDO> selectAllOrderByPriority() {
        return selectList(new LambdaQueryWrapper<>(KbTermDO.class)
            .eq(KbTermDO::getEnabled, true)
            .orderByDesc(KbTermDO::getPriority, KbTermDO::getId));
    }
}
