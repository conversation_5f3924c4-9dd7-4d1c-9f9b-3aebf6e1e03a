package cn.savas.hub.module.algocell.service.engine;

import cn.savas.hub.module.algocell.domain.*;
import cn.savas.hub.module.algocell.enums.MatchModeEnum;
import cn.savas.hub.module.algocell.utils.TextNormalizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * 匹配引擎核心实现
 *
 * 基于文档中的算法实现行级匹配功能：
 * 1. 文本标准化
 * 2. Trie树字符串匹配 + 正则表达式匹配
 * 3. 证据收集和层级补全
 * 4. 候选指标召回
 * 5. 匹配率计算和评分
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MatchEngine {

    /**
     * 匹配单行数据
     *
     * @param rowData 行数据，键为列名，值为列值
     * @param kbRuntime 知识库运行时
     * @param indicators 候选指标列表
     * @return 匹配结果列表，按匹配率和得分排序
     */
    public List<MatchResult> matchRow(Map<String, String> rowData,
                                      KbRuntime kbRuntime,
                                      List<Indicator> indicators) {

        long startTime = System.currentTimeMillis();

        try {
            // 1. 构建文档并保留列名信息
            DocumentInfo docInfo = buildDocument(rowData);

            // 2. 执行匹配获取证据
            List<Evidence> evidences = findEvidences(docInfo, kbRuntime);

            // 3. 层级补全和归一化
            Set<String> hitFeatureValues = expandEvidences(evidences, kbRuntime);

            // 4. 候选指标召回
            Set<Long> candidateIndicatorIds = recallCandidateIndicators(hitFeatureValues, kbRuntime);

            // 5. 计算匹配结果
            List<MatchResult> results = calculateMatchResults(candidateIndicatorIds,
                                                            hitFeatureValues,
                                                            evidences,
                                                            kbRuntime,
                                                            indicators);

            // 6. 排序结果
            sortResults(results);

            long duration = System.currentTimeMillis() - startTime;
            log.debug("行匹配完成，耗时: {}ms，候选指标: {}，匹配结果: {}",
                     duration, candidateIndicatorIds.size(), results.size());

            return results;

        } catch (Exception e) {
            log.error("行匹配失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建文档信息
     *
     * 将行数据拼接成文档，同时保留位置到列名的映射
     */
    private DocumentInfo buildDocument(Map<String, String> rowData) {
        StringBuilder docBuilder = new StringBuilder();
        Map<Integer, String> positionToColumn = new HashMap<>();

        int offset = 0;
        for (Map.Entry<String, String> entry : rowData.entrySet()) {
            String columnName = entry.getKey();
            String value = entry.getValue();

            if (value == null) {
                value = "";
            }

            // 标准化文本
            String normalizedValue = TextNormalizer.normalize(value);

            // 添加列名前缀以便识别
            String columnText = columnName + ":" + normalizedValue + "\n";

            // 记录位置映射（只映射值部分，跳过列名部分）
            int valueStart = offset + columnName.length() + 1; // +1 for ':'
            for (int i = 0; i < normalizedValue.length(); i++) {
                positionToColumn.put(valueStart + i, columnName);
            }

            docBuilder.append(columnText);
            offset += columnText.length();
        }

        return new DocumentInfo(docBuilder.toString(), positionToColumn);
    }

    /**
     * 查找匹配证据
     *
     * 使用Trie树和正则表达式查找所有匹配的术语
     */
    private List<Evidence> findEvidences(DocumentInfo docInfo, KbRuntime kbRuntime) {
        List<Evidence> evidences = new ArrayList<>();
        String document = docInfo.getDocument();

        // 1. Trie树字符串匹配
        List<Trie.MatchResult> trieMatches = kbRuntime.getTrie().findAll(document);
        for (Trie.MatchResult match : trieMatches) {
            List<TermEntry> termEntries = kbRuntime.getTermIndex()
                .getOrDefault(match.term.toLowerCase(), Collections.emptyList());

            for (TermEntry termEntry : termEntries) {
                Evidence evidence = Evidence.builder()
                    .featureCode(termEntry.getFeatureCode())
                    .valueCode(termEntry.getValueCode())
                    .sourceTerm(match.term)
                    .start(match.start)
                    .end(match.end)
                    .colName(docInfo.getColumnAt(match.start))
                    .from("STRING")
                    .confidence(1.0)
                    .originalText(match.originalText)
                    .build();

                evidences.add(evidence);
            }
        }

        // 2. 正则表达式匹配
        for (KbRuntime.CompiledRegex compiledRegex : kbRuntime.getRegexList()) {
            Matcher matcher = compiledRegex.getPattern().matcher(document);

            while (matcher.find()) {
                Evidence evidence = Evidence.builder()
                    .featureCode(compiledRegex.getFeatureCode())
                    .valueCode(compiledRegex.getValueCode())
                    .sourceTerm(matcher.group())
                    .start(matcher.start())
                    .end(matcher.end())
                    .colName(docInfo.getColumnAt(matcher.start()))
                    .from("REGEX")
                    .confidence(0.95) // 正则匹配置信度稍低
                    .originalText(matcher.group())
                    .build();

                evidences.add(evidence);
            }
        }

        log.debug("找到匹配证据: {} 个", evidences.size());
        return evidences;
    }

    /**
     * 扩展证据并进行层级补全
     *
     * 对匹配到的要素值进行层级扩展，补充父值命中
     */
    private Set<String> expandEvidences(List<Evidence> evidences, KbRuntime kbRuntime) {
        Set<String> hitFeatureValues = new HashSet<>();

        for (Evidence evidence : evidences) {
            String featureValueKey = evidence.getFeatureValueKey();
            hitFeatureValues.add(featureValueKey);

            // 补充祖先链
            List<String> ancestors = kbRuntime.getAncestors(evidence.getFeatureCode(),
                                                           evidence.getValueCode());
            for (String ancestorValue : ancestors) {
                String ancestorKey = KbRuntime.buildFeatureValueKey(evidence.getFeatureCode(),
                                                                   ancestorValue);
                hitFeatureValues.add(ancestorKey);
            }
        }

        log.debug("层级补全后的要素值: {} 个", hitFeatureValues.size());
        return hitFeatureValues;
    }

    /**
     * 召回候选指标
     *
     * 基于命中的要素值召回相关的指标
     */
    private Set<Long> recallCandidateIndicators(Set<String> hitFeatureValues, KbRuntime kbRuntime) {
        Set<Long> candidateIds = new HashSet<>();

        for (String featureValueKey : hitFeatureValues) {
            Set<Long> relatedIndicators = kbRuntime.getRelatedIndicators(featureValueKey);
            candidateIds.addAll(relatedIndicators);
        }

        log.debug("召回候选指标: {} 个", candidateIds.size());
        return candidateIds;
    }

    /**
     * 计算匹配结果
     *
     * 对每个候选指标计算匹配率和得分
     */
    private List<MatchResult> calculateMatchResults(Set<Long> candidateIndicatorIds,
                                                   Set<String> hitFeatureValues,
                                                   List<Evidence> evidences,
                                                   KbRuntime kbRuntime,
                                                   List<Indicator> indicators) {

        Map<Long, Indicator> indicatorMap = indicators.stream()
            .collect(Collectors.toMap(Indicator::getId, i -> i));

        List<MatchResult> results = new ArrayList<>();

        for (Long indicatorId : candidateIndicatorIds) {
            Indicator indicator = indicatorMap.get(indicatorId);
            if (indicator == null || !indicator.isAvailable()) {
                continue;
            }

            List<IndicatorFeatureReq> requirements = kbRuntime.getIndicatorRequirements(indicatorId);
            if (requirements.isEmpty()) {
                continue;
            }

            MatchResult result = calculateSingleIndicatorMatch(indicator, requirements,
                                                             hitFeatureValues, evidences, kbRuntime);
            if (result != null) {
                results.add(result);
            }
        }

        return results;
    }

    /**
     * 计算单个指标的匹配结果
     */
    private MatchResult calculateSingleIndicatorMatch(Indicator indicator,
                                                     List<IndicatorFeatureReq> requirements,
                                                     Set<String> hitFeatureValues,
                                                     List<Evidence> evidences,
                                                     KbRuntime kbRuntime) {

        int totalRequirements = 0;
        int matchedRequirements = 0;
        double totalScore = 0.0;
        List<String> missingFeatures = new ArrayList<>();

        for (IndicatorFeatureReq req : requirements) {
            if (!req.isRequired()) {
                continue; // 跳过非必需要素
            }

            totalRequirements++;
            double featureWeight = kbRuntime.getFeatureWeight(req.getFeatureCode());

            MatchResult.FeatureMatchInfo matchInfo = evaluateFeatureMatch(req, hitFeatureValues, kbRuntime);

            if (matchInfo.isMatched()) {
                matchedRequirements++;
                totalScore += featureWeight * matchInfo.getScore();
            } else {
                String missingDesc = req.getFeatureCode();
                if (req.getRequiredValueCode() != null) {
                    missingDesc += "=" + req.getRequiredValueCode();
                } else {
                    missingDesc += "(ANY)";
                }
                missingFeatures.add(missingDesc);
            }
        }

        if (totalRequirements == 0) {
            return null; // 没有必需要素
        }

        double matchRate = (double) matchedRequirements / totalRequirements;
        double normalizedScore = Math.min(1.0, totalScore / Math.max(1.0, totalRequirements));

        // 过滤相关证据
        List<Evidence> relevantEvidences = evidences.stream()
            .filter(e -> requirements.stream()
                .anyMatch(req -> req.getFeatureCode().equals(e.getFeatureCode())))
            .collect(Collectors.toList());

        return MatchResult.builder()
            .indicatorId(indicator.getId())
            .indicatorCode(indicator.getCode())
            .indicatorName(indicator.getName())
            .matchRate(matchRate)
            .score(normalizedScore)
            .matchedCount(matchedRequirements)
            .totalCount(totalRequirements)
            .missingFeatures(missingFeatures)
            .evidences(relevantEvidences)
            .build();
    }

    /**
     * 评估单个要素的匹配情况
     */
    private MatchResult.FeatureMatchInfo evaluateFeatureMatch(IndicatorFeatureReq req,
                                                             Set<String> hitFeatureValues,
                                                             KbRuntime kbRuntime) {

        MatchModeEnum mode = req.getSafeMatchMode();
        String featureCode = req.getFeatureCode();
        String requiredValueCode = req.getRequiredValueCode();

        if (req.isAnyValueAcceptable()) {
            // ANY模式：该要素任意值出现即可
            boolean hasAnyValue = hitFeatureValues.stream()
                .anyMatch(fv -> fv.startsWith(featureCode + "::"));
            return new MatchResult.FeatureMatchInfo(hasAnyValue, hasAnyValue ? 0.7 : 0.0);
        }

        String exactKey = KbRuntime.buildFeatureValueKey(featureCode, requiredValueCode);

        switch (mode) {
            case EXACT:
                boolean exactMatch = hitFeatureValues.contains(exactKey);
                return new MatchResult.FeatureMatchInfo(exactMatch, exactMatch ? 1.0 : 0.0);

            case ANCESTOR_OK:
                // 命中该值或其任意子值
                boolean exactOrChild = hitFeatureValues.contains(exactKey) ||
                    hitFeatureValues.stream().anyMatch(fv ->
                        fv.startsWith(featureCode + "::") &&
                        !fv.equals(exactKey) &&
                        isDescendantValue(kbRuntime, featureCode, fv, requiredValueCode));

                if (hitFeatureValues.contains(exactKey)) {
                    return new MatchResult.FeatureMatchInfo(true, 0.9);
                } else if (exactOrChild) {
                    return new MatchResult.FeatureMatchInfo(true, 1.0);
                } else {
                    return new MatchResult.FeatureMatchInfo(false, 0.0);
                }

            case DESCENDANT_OK:
                // 命中该值或其任意父值
                boolean exactOrParent = hitFeatureValues.contains(exactKey) ||
                    hitFeatureValues.stream().anyMatch(fv ->
                        fv.startsWith(featureCode + "::") &&
                        !fv.equals(exactKey) &&
                        isAncestorValue(kbRuntime, featureCode, fv, requiredValueCode));

                if (hitFeatureValues.contains(exactKey)) {
                    return new MatchResult.FeatureMatchInfo(true, 1.0);
                } else if (exactOrParent) {
                    return new MatchResult.FeatureMatchInfo(true, 0.95);
                } else {
                    return new MatchResult.FeatureMatchInfo(false, 0.0);
                }

            default:
                return new MatchResult.FeatureMatchInfo(false, 0.0);
        }
    }

    /**
     * 判断是否为后代值
     */
    private boolean isDescendantValue(KbRuntime kbRuntime, String featureCode,
                                     String featureValueKey, String ancestorValueCode) {
        String[] parts = featureValueKey.split("::", 2);
        if (parts.length != 2) {
            return false;
        }

        String valueCode = parts[1];
        return kbRuntime.isAncestor(featureCode, valueCode, ancestorValueCode);
    }

    /**
     * 判断是否为祖先值
     */
    private boolean isAncestorValue(KbRuntime kbRuntime, String featureCode,
                                   String featureValueKey, String descendantValueCode) {
        String[] parts = featureValueKey.split("::", 2);
        if (parts.length != 2) {
            return false;
        }

        String valueCode = parts[1];
        return kbRuntime.isAncestor(featureCode, descendantValueCode, valueCode);
    }

    /**
     * 排序匹配结果
     *
     * 按匹配率、得分、指标复杂度排序
     */
    private void sortResults(List<MatchResult> results) {
        results.sort(Comparator
            .comparingDouble((MatchResult r) -> r.getSafeMatchRate()).reversed()
            .thenComparingDouble(r -> r.getSafeScore()).reversed()
            .thenComparingInt(r -> r.getTotalCount() != null ? r.getTotalCount() : 0).reversed()
        );
    }

    /**
     * 文档信息包装类
     */
    private static class DocumentInfo {
        private final String document;
        private final Map<Integer, String> positionToColumn;

        public DocumentInfo(String document, Map<Integer, String> positionToColumn) {
            this.document = document;
            this.positionToColumn = positionToColumn;
        }

        public String getDocument() {
            return document;
        }

        public String getColumnAt(int position) {
            return positionToColumn.getOrDefault(position, "unknown");
        }
    }
}
