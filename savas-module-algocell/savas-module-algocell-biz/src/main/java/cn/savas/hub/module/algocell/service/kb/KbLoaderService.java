package cn.savas.hub.module.algocell.service.kb;

import cn.savas.hub.module.algocell.dal.dataobject.kb.*;
import cn.savas.hub.module.algocell.dal.mapper.*;
import cn.savas.hub.module.algocell.domain.FeatureValue;
import cn.savas.hub.module.algocell.domain.Indicator;
import cn.savas.hub.module.algocell.domain.IndicatorFeatureReq;
import cn.savas.hub.module.algocell.domain.TermEntry;
import cn.savas.hub.module.algocell.enums.MatchModeEnum;
import cn.savas.hub.module.algocell.enums.TermTypeEnum;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识库加载服务
 *
 * 负责从数据库加载知识库数据并构建运行时结构
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KbLoaderService {

    @Resource
    private KbFeatureMapper kbFeatureMapper;

    @Resource
    private KbValueMapper kbValueMapper;

    @Resource
    private KbTermMapper kbTermMapper;

    @Resource
    private IndicatorMapper indicatorMapper;

    @Resource
    private IndicatorFeatureReqMapper indicatorFeatureReqMapper;

    /**
     * 从数据库加载知识库数据
     *
     * @return 构建好的知识库运行时
     */
    public KbRuntime loadFromDatabase() {
        long startTime = System.currentTimeMillis();
        log.info("开始加载知识库数据...");

        try {
            KbRuntime kbRuntime = new KbRuntime();

            // 1. 加载要素值和层级关系
            loadFeatureValues(kbRuntime);

            // 2. 加载术语条目
            loadTermEntries(kbRuntime);

            // 3. 加载指标和要素要求
            loadIndicatorsAndRequirements(kbRuntime);

            // 4. 设置版本信息
            kbRuntime.setVersion(generateVersion());
            kbRuntime.setLastUpdateTime(System.currentTimeMillis());

            long duration = System.currentTimeMillis() - startTime;
            log.info("知识库加载完成，耗时: {}ms，统计信息: {}", duration, kbRuntime.getStatistics());

            return kbRuntime;

        } catch (Exception e) {
            log.error("知识库加载失败", e);
            throw new RuntimeException("知识库加载失败", e);
        }
    }

    /**
     * 加载要素值和层级关系
     */
    private void loadFeatureValues(KbRuntime kbRuntime) {
        log.debug("加载要素值...");

        List<KbValueDO> valueDOs = kbValueMapper.selectEnabledValues();

        for (KbValueDO valueDO : valueDOs) {
            FeatureValue featureValue = FeatureValue.builder()
                .featureCode(valueDO.getFeatureCode())
                .valueCode(valueDO.getCode())
                .displayName(valueDO.getName())
                .parentValueCode(valueDO.getParentCode())
                .build();

            kbRuntime.addFeatureValue(featureValue);
        }

        log.debug("加载要素值完成，数量: {}", valueDOs.size());
    }

    /**
     * 加载术语条目
     */
    private void loadTermEntries(KbRuntime kbRuntime) {
        log.debug("加载术语条目...");

        List<KbTermDO> termDOs = kbTermMapper.selectEnabledTerms();

        for (KbTermDO termDO : termDOs) {
            TermTypeEnum termType = TermTypeEnum.getByCode(termDO.getTermType());
            if (termType == null) {
                log.warn("未知的术语类型: {}, 跳过术语: {}", termDO.getTermType(), termDO.getExpr());
                continue;
            }

            TermEntry termEntry = TermEntry.builder()
                .featureCode(termDO.getFeatureCode())
                .valueCode(termDO.getValueCode())
                .type(termType)
                .expr(termDO.getExpr())
                .flags(termDO.getFlags())
                .build();

            try {
                kbRuntime.addTermEntry(termEntry);
            } catch (Exception e) {
                log.error("添加术语失败: {}", termDO.getExpr(), e);
                // 继续处理其他术语
            }
        }

        log.debug("加载术语条目完成，数量: {}", termDOs.size());
    }

    /**
     * 加载指标和要素要求
     */
    private void loadIndicatorsAndRequirements(KbRuntime kbRuntime) {
        log.debug("加载指标和要素要求...");

        // 加载所有启用的指标
        List<IndicatorDO> indicatorDOs = indicatorMapper.selectEnabledIndicators();

        // 加载所有要素要求
        List<IndicatorFeatureReqDO> reqDOs = indicatorFeatureReqMapper.selectAll();

        // 按指标ID分组
        Map<Long, List<IndicatorFeatureReqDO>> reqsByIndicator = reqDOs.stream()
            .collect(Collectors.groupingBy(IndicatorFeatureReqDO::getIndicatorId));

        // 处理每个指标
        for (IndicatorDO indicatorDO : indicatorDOs) {
            List<IndicatorFeatureReqDO> indicatorReqs = reqsByIndicator.get(indicatorDO.getId());
            if (indicatorReqs == null || indicatorReqs.isEmpty()) {
                log.warn("指标 {} 没有要素要求配置，跳过", indicatorDO.getCode());
                continue;
            }

            // 处理该指标的要素要求
            for (IndicatorFeatureReqDO reqDO : indicatorReqs) {
                MatchModeEnum matchMode = MatchModeEnum.getByCode(reqDO.getMatchMode());
                if (matchMode == null) {
                    log.warn("未知的匹配模式: {}, 跳过要求: {}:{}",
                            reqDO.getMatchMode(), reqDO.getFeatureCode(), reqDO.getRequiredValueCode());
                    continue;
                }

                IndicatorFeatureReq requirement = IndicatorFeatureReq.builder()
                    .indicatorId(reqDO.getIndicatorId())
                    .featureCode(reqDO.getFeatureCode())
                    .requiredValueCode(reqDO.getRequiredValueCode())
                    .matchMode(matchMode)
                    .weight(reqDO.getWeight())
                    .required(reqDO.getRequired())
                    .build();

                kbRuntime.addIndicatorRequirement(indicatorDO.getId(), requirement);
            }
        }

        log.debug("加载指标和要素要求完成，指标数量: {}, 要求数量: {}",
                 indicatorDOs.size(), reqDOs.size());
    }

    /**
     * 生成版本号
     *
     * @return 版本号字符串
     */
    private String generateVersion() {
        return "KB_" + LocalDateTime.now().toString().replace(":", "").replace("-", "").replace(".", "");
    }

    /**
     * 获取所有启用的指标
     *
     * @return 指标列表
     */
    public List<Indicator> loadIndicators() {
        List<IndicatorDO> indicatorDOs = indicatorMapper.selectEnabledIndicators();

        return indicatorDOs.stream()
            .map(this::convertToIndicator)
            .collect(Collectors.toList());
    }

    /**
     * 根据ID集合获取指标
     *
     * @param indicatorIds 指标ID集合
     * @return 指标列表
     */
    public List<Indicator> loadIndicatorsByIds(java.util.Set<Long> indicatorIds) {
        List<IndicatorDO> indicatorDOs = indicatorMapper.selectByIds(indicatorIds);

        return indicatorDOs.stream()
            .map(this::convertToIndicator)
            .collect(Collectors.toList());
    }

    /**
     * 转换DO到领域对象
     */
    private Indicator convertToIndicator(IndicatorDO indicatorDO) {
        return Indicator.builder()
            .id(indicatorDO.getId())
            .code(indicatorDO.getCode())
            .name(indicatorDO.getName())
            .description(indicatorDO.getDescription())
            .enabled(indicatorDO.getEnabled())
            .sortWeight(indicatorDO.getSortWeight())
            .build();
    }
}
