package cn.savas.hub.module.algocell.service.engine;

import cn.savas.hub.module.algocell.domain.FeatureValue;
import cn.savas.hub.module.algocell.domain.IndicatorFeatureReq;
import cn.savas.hub.module.algocell.domain.TermEntry;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 知识库运行时
 *
 * 管理知识库的内存结构，包括Trie树、正则表达式、倒排索引等
 * 提供高效的匹配查询功能
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class KbRuntime {

    /**
     * 字符串术语的Trie树
     */
    private Trie trie = new Trie(false); // 不区分大小写

    /**
     * 预编译的正则表达式列表
     */
    private List<CompiledRegex> regexList = new ArrayList<>();

    /**
     * 要素值索引：featureCode -> List<FeatureValue>
     */
    private Map<String, List<FeatureValue>> valuesByFeature = new ConcurrentHashMap<>();

    /**
     * 要素值快速查找：featureCode::valueCode -> FeatureValue
     */
    private Map<String, FeatureValue> featureValueIndex = new ConcurrentHashMap<>();

    /**
     * 术语索引：term -> List<TermEntry> (仅STRING类型)
     */
    private Map<String, List<TermEntry>> termIndex = new ConcurrentHashMap<>();

    /**
     * 倒排索引：featureCode::valueCode -> Set<Long> (指标ID集合)
     */
    private Map<String, Set<Long>> valueToIndicators = new ConcurrentHashMap<>();

    /**
     * 指标要素要求：indicatorId -> List<IndicatorFeatureReq>
     */
    private Map<Long, List<IndicatorFeatureReq>> indicatorRequirements = new ConcurrentHashMap<>();

    /**
     * 要素权重配置：featureCode -> weight
     */
    private Map<String, Double> featureWeights = new ConcurrentHashMap<>();

    /**
     * 父子关系映射：featureCode::valueCode -> parentValueCode
     */
    private Map<String, String> parentMapping = new ConcurrentHashMap<>();

    /**
     * 知识库版本号
     */
    private String version;

    /**
     * 最后更新时间
     */
    private long lastUpdateTime;

    /**
     * 预编译正则表达式包装类
     */
    @Data
    public static class CompiledRegex {
        /**
         * 编译后的正则模式
         */
        private Pattern pattern;

        /**
         * 要素代码
         */
        private String featureCode;

        /**
         * 要素值代码
         */
        private String valueCode;

        /**
         * 原始表达式
         */
        private String expression;

        /**
         * 正则标志
         */
        private int flags;

        public CompiledRegex(String featureCode, String valueCode, String expression, int flags) {
            this.featureCode = featureCode;
            this.valueCode = valueCode;
            this.expression = expression;
            this.flags = flags;

            try {
                this.pattern = Pattern.compile(expression, flags);
                log.debug("编译正则表达式成功: {} -> {}::{}", expression, featureCode, valueCode);
            } catch (Exception e) {
                log.error("编译正则表达式失败: {}", expression, e);
                throw new RuntimeException("正则表达式编译失败: " + expression, e);
            }
        }
    }

    /**
     * 生成要素值的唯一键
     *
     * @param featureCode 要素代码
     * @param valueCode 要素值代码
     * @return 唯一键字符串
     */
    public static String buildFeatureValueKey(String featureCode, String valueCode) {
        return featureCode + "::" + valueCode;
    }

    /**
     * 添加要素值
     *
     * @param featureValue 要素值对象
     */
    public void addFeatureValue(FeatureValue featureValue) {
        String key = buildFeatureValueKey(featureValue.getFeatureCode(), featureValue.getValueCode());
        featureValueIndex.put(key, featureValue);

        // 按要素分组
        valuesByFeature.computeIfAbsent(featureValue.getFeatureCode(), k -> new ArrayList<>())
                      .add(featureValue);

        // 建立父子关系映射
        if (featureValue.getParentValueCode() != null) {
            parentMapping.put(key, featureValue.getParentValueCode());
        }

        log.debug("添加要素值: {}", key);
    }

    /**
     * 添加术语条目
     *
     * @param termEntry 术语条目
     */
    public void addTermEntry(TermEntry termEntry) {
        if (termEntry.isStringType()) {
            // 字符串术语加入Trie树
            trie.insert(termEntry.getExpr());

            // 建立术语到要素值的映射
            termIndex.computeIfAbsent(termEntry.getExpr().toLowerCase(), k -> new ArrayList<>())
                     .add(termEntry);
        } else if (termEntry.isRegexType()) {
            // 正则术语预编译
            try {
                CompiledRegex compiledRegex = new CompiledRegex(
                    termEntry.getFeatureCode(),
                    termEntry.getValueCode(),
                    termEntry.getExpr(),
                    termEntry.getSafeFlags()
                );
                regexList.add(compiledRegex);
            } catch (Exception e) {
                log.error("添加正则术语失败: {}", termEntry.getExpr(), e);
            }
        }

        log.debug("添加术语: {} -> {}::{}", termEntry.getExpr(),
                 termEntry.getFeatureCode(), termEntry.getValueCode());
    }

    /**
     * 添加指标要素要求
     *
     * @param indicatorId 指标ID
     * @param requirement 要素要求
     */
    public void addIndicatorRequirement(Long indicatorId, IndicatorFeatureReq requirement) {
        indicatorRequirements.computeIfAbsent(indicatorId, k -> new ArrayList<>())
                            .add(requirement);

        // 建立倒排索引
        String featureValueKey;
        if (requirement.getRequiredValueCode() != null) {
            featureValueKey = buildFeatureValueKey(requirement.getFeatureCode(),
                                                  requirement.getRequiredValueCode());
        } else {
            // 如果没有指定具体值，使用要素代码作为键
            featureValueKey = requirement.getFeatureCode();
        }

        valueToIndicators.computeIfAbsent(featureValueKey, k -> new HashSet<>())
                         .add(indicatorId);

        log.debug("添加指标要求: {} -> {}", indicatorId, featureValueKey);
    }

    /**
     * 设置要素权重
     *
     * @param featureCode 要素代码
     * @param weight 权重值
     */
    public void setFeatureWeight(String featureCode, Double weight) {
        featureWeights.put(featureCode, weight);
    }

    /**
     * 获取要素权重
     *
     * @param featureCode 要素代码
     * @return 权重值，默认为1.0
     */
    public double getFeatureWeight(String featureCode) {
        return featureWeights.getOrDefault(featureCode, 1.0);
    }

    /**
     * 获取要素值的父值代码
     *
     * @param featureCode 要素代码
     * @param valueCode 要素值代码
     * @return 父值代码，如果没有则返回null
     */
    public String getParentValueCode(String featureCode, String valueCode) {
        String key = buildFeatureValueKey(featureCode, valueCode);
        return parentMapping.get(key);
    }

    /**
     * 判断是否为祖先关系
     *
     * @param featureCode 要素代码
     * @param childValueCode 子值代码
     * @param ancestorValueCode 祖先值代码
     * @return 如果是祖先关系返回true
     */
    public boolean isAncestor(String featureCode, String childValueCode, String ancestorValueCode) {
        String current = childValueCode;
        while (current != null) {
            String parent = getParentValueCode(featureCode, current);
            if (ancestorValueCode.equals(parent)) {
                return true;
            }
            current = parent;
        }
        return false;
    }

    /**
     * 获取所有祖先值代码
     *
     * @param featureCode 要素代码
     * @param valueCode 要素值代码
     * @return 祖先值代码列表（从直接父级到根级）
     */
    public List<String> getAncestors(String featureCode, String valueCode) {
        List<String> ancestors = new ArrayList<>();
        String current = getParentValueCode(featureCode, valueCode);

        while (current != null) {
            ancestors.add(current);
            current = getParentValueCode(featureCode, current);
        }

        return ancestors;
    }

    /**
     * 清空所有数据
     */
    public void clear() {
        trie.clear();
        regexList.clear();
        valuesByFeature.clear();
        featureValueIndex.clear();
        termIndex.clear();
        valueToIndicators.clear();
        indicatorRequirements.clear();
        featureWeights.clear();
        parentMapping.clear();

        log.info("知识库运行时已清空");
    }

    /**
     * 获取指标相关的要素值集合
     *
     * @param featureValueKey 要素值键
     * @return 相关的指标ID集合
     */
    public Set<Long> getRelatedIndicators(String featureValueKey) {
        return valueToIndicators.getOrDefault(featureValueKey, Collections.emptySet());
    }

    /**
     * 获取指标的要素要求列表
     *
     * @param indicatorId 指标ID
     * @return 要素要求列表
     */
    public List<IndicatorFeatureReq> getIndicatorRequirements(Long indicatorId) {
        return indicatorRequirements.getOrDefault(indicatorId, Collections.emptyList());
    }

    /**
     * 获取要素权重映射（用于Redis缓存）
     *
     * @return 要素权重映射
     */
    public Map<String, Double> getFeatureWeights() {
        return new HashMap<>(featureWeights);
    }

    /**
     * 获取倒排索引映射（用于Redis缓存）
     *
     * @return 倒排索引映射
     */
    public Map<String, Set<Long>> getValueToIndicators() {
        Map<String, Set<Long>> result = new HashMap<>();
        for (Map.Entry<String, Set<Long>> entry : valueToIndicators.entrySet()) {
            result.put(entry.getKey(), new HashSet<>(entry.getValue()));
        }
        return result;
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息Map
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("version", version);
        stats.put("lastUpdateTime", lastUpdateTime);
        stats.put("stringTermCount", trie.getTermCount());
        stats.put("regexTermCount", regexList.size());
        stats.put("featureCount", valuesByFeature.size());
        stats.put("valueCount", featureValueIndex.size());
        stats.put("indicatorCount", indicatorRequirements.size());
        stats.put("trieNodeCount", trie.getNodeCount());

        return stats;
    }
}
