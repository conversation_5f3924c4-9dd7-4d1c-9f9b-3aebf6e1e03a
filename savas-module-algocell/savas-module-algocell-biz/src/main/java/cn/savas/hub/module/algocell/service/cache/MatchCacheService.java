package cn.savas.hub.module.algocell.service.cache;

import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 匹配缓存服务
 *
 * 提供文本标准化和匹配结果的缓存功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MatchCacheService {

    @Resource
    private AlgoCellProperties properties;

    @Resource
    private RedisCacheService redisCacheService;

    /**
     * 文本标准化结果缓存
     */
    private Cache<String, String> normalizationCache;

    /**
     * 匹配结果缓存
     */
    private Cache<String, List<MatchResult>> matchResultCache;

    /**
     * 统计信息
     */
    private final Map<String, Long> statistics = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (!properties.getCache().getEnabled()) {
            log.info("缓存功能已禁用");
            return;
        }

        initCaches();
        log.info("匹配缓存服务初始化完成");
    }

    /**
     * 初始化缓存
     */
    private void initCaches() {
        AlgoCellProperties.CacheConfig cacheConfig = properties.getCache();

        // 文本标准化缓存
        normalizationCache = Caffeine.newBuilder()
            .maximumSize(cacheConfig.getNormalizationCacheSize())
            .expireAfterWrite(Duration.ofMinutes(cacheConfig.getExpireMinutes()))
            .recordStats()
            .build();

        // 匹配结果缓存
        matchResultCache = Caffeine.newBuilder()
            .maximumSize(cacheConfig.getMatchResultCacheSize())
            .expireAfterWrite(Duration.ofMinutes(cacheConfig.getExpireMinutes()))
            .recordStats()
            .build();

        log.info("缓存初始化完成 - 标准化缓存大小: {}, 匹配结果缓存大小: {}, 过期时间: {}分钟",
                cacheConfig.getNormalizationCacheSize(),
                cacheConfig.getMatchResultCacheSize(),
                cacheConfig.getExpireMinutes());
    }

    /**
     * 获取标准化文本缓存
     *
     * @param originalText 原始文本
     * @return 标准化后的文本，如果缓存中没有则返回null
     */
    public String getNormalizedText(String originalText) {
        if (!isCacheEnabled() || originalText == null) {
            return null;
        }

        String normalized = normalizationCache.getIfPresent(originalText);
        if (normalized != null) {
            incrementStat("normalization_cache_hit");
        } else {
            incrementStat("normalization_cache_miss");
        }

        return normalized;
    }

    /**
     * 缓存标准化文本
     *
     * @param originalText 原始文本
     * @param normalizedText 标准化后的文本
     */
    public void putNormalizedText(String originalText, String normalizedText) {
        if (!isCacheEnabled() || originalText == null || normalizedText == null) {
            return;
        }

        normalizationCache.put(originalText, normalizedText);
        incrementStat("normalization_cache_put");
    }

    /**
     * 获取匹配结果缓存
     *
     * @param cacheKey 缓存键
     * @return 匹配结果列表，如果缓存中没有则返回null
     */
    public List<MatchResult> getMatchResults(String cacheKey) {
        if (!isCacheEnabled() || cacheKey == null) {
            return null;
        }

        // 优先从本地缓存获取
        List<MatchResult> results = matchResultCache.getIfPresent(cacheKey);
        if (results != null) {
            incrementStat("match_result_local_cache_hit");
            return results;
        }

        // 从Redis缓存获取
        if (redisCacheService.isAvailable()) {
            try {
                results = getMatchResultsFromRedis(cacheKey);
                if (results != null) {
                    // 回填到本地缓存
                    matchResultCache.put(cacheKey, results);
                    incrementStat("match_result_redis_cache_hit");
                    return results;
                }
            } catch (Exception e) {
                log.warn("从Redis获取匹配结果失败: {}", e.getMessage());
            }
        }

        incrementStat("match_result_cache_miss");
        return null;
    }

    /**
     * 缓存匹配结果
     *
     * @param cacheKey 缓存键
     * @param results 匹配结果列表
     */
    public void putMatchResults(String cacheKey, List<MatchResult> results) {
        if (!isCacheEnabled() || cacheKey == null || results == null) {
            return;
        }

        // 缓存到本地
        matchResultCache.put(cacheKey, results);
        incrementStat("match_result_local_cache_put");

        // 缓存到Redis
        if (redisCacheService.isAvailable()) {
            try {
                putMatchResultsToRedis(cacheKey, results);
                incrementStat("match_result_redis_cache_put");
            } catch (Exception e) {
                log.warn("缓存匹配结果到Redis失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 生成匹配结果缓存键
     *
     * @param rowData 行数据
     * @param threshold 阈值
     * @param maxResults 最大结果数
     * @return 缓存键
     */
    public String generateMatchCacheKey(Map<String, String> rowData, double threshold, int maxResults) {
        if (rowData == null) {
            return null;
        }

        // 简单的缓存键生成策略
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("match:");

        // 对行数据进行排序以确保一致性
        rowData.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> keyBuilder.append(entry.getKey())
                .append("=")
                .append(entry.getValue())
                .append(";"));

        keyBuilder.append("threshold=").append(threshold);
        keyBuilder.append("maxResults=").append(maxResults);

        return keyBuilder.toString();
    }

    /**
     * 清理所有缓存
     */
    public void clearAll() {
        if (!isCacheEnabled()) {
            return;
        }

        if (normalizationCache != null) {
            normalizationCache.invalidateAll();
        }

        if (matchResultCache != null) {
            matchResultCache.invalidateAll();
        }

        // 清理Redis缓存
        if (redisCacheService.isAvailable()) {
            try {
                redisCacheService.clearKbCache();
            } catch (Exception e) {
                log.warn("清理Redis缓存失败: {}", e.getMessage());
            }
        }

        statistics.clear();
        log.info("所有缓存已清理");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息Map
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>(statistics);

        if (isCacheEnabled()) {
            if (normalizationCache != null) {
                CacheStats normStats = normalizationCache.stats();
                stats.put("normalization_cache_size", normalizationCache.estimatedSize());
                stats.put("normalization_hit_rate", normStats.hitRate());
                stats.put("normalization_eviction_count", normStats.evictionCount());
            }

            if (matchResultCache != null) {
                CacheStats matchStats = matchResultCache.stats();
                stats.put("match_result_cache_size", matchResultCache.estimatedSize());
                stats.put("match_result_hit_rate", matchStats.hitRate());
                stats.put("match_result_eviction_count", matchStats.evictionCount());
            }
        }

        return stats;
    }

    /**
     * 判断缓存是否启用
     */
    private boolean isCacheEnabled() {
        return properties.getCache().getEnabled() &&
               properties.getCache().getEnableLocalCache();
    }

    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        statistics.merge(key, 1L, Long::sum);
    }

    /**
     * 从Redis获取匹配结果
     */
    private List<MatchResult> getMatchResultsFromRedis(String cacheKey) {
        try {
            return redisCacheService.getCachedMatchResults(cacheKey, new TypeReference<List<MatchResult>>() {});
        } catch (Exception e) {
            log.warn("从Redis获取匹配结果失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将匹配结果缓存到Redis
     */
    private void putMatchResultsToRedis(String cacheKey, List<MatchResult> results) {
        try {
            redisCacheService.cacheMatchResults(cacheKey, results);
        } catch (Exception e) {
            log.warn("缓存匹配结果到Redis失败: {}", e.getMessage());
        }
    }
}
