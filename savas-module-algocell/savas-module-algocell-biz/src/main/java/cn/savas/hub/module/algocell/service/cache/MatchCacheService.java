package cn.savas.hub.module.algocell.service.cache;

import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 匹配缓存服务
 *
 * 简化版缓存服务，全部使用Redis缓存，去除本地缓存复杂性
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MatchCacheService {

    @Resource
    private AlgoCellProperties properties;

    @Resource
    private RedisCacheService redisCacheService;

    /**
     * 统计信息
     */
    private final Map<String, Long> statistics = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (!properties.getCache().getEnabled()) {
            log.info("缓存功能已禁用");
            return;
        }

        log.info("匹配缓存服务初始化完成 - 使用Redis缓存");
    }

    /**
     * 获取标准化文本缓存
     *
     * @param originalText 原始文本
     * @return 标准化后的文本，如果缓存中没有则返回null
     */
    public String getNormalizedText(String originalText) {
        if (!isCacheEnabled() || originalText == null) {
            return null;
        }

        try {
            String normalized = redisCacheService.getCachedNormalizedText(originalText);
            if (normalized != null) {
                incrementStat("normalization_cache_hit");
            } else {
                incrementStat("normalization_cache_miss");
            }
            return normalized;
        } catch (Exception e) {
            log.warn("获取标准化文本缓存失败: {}", e.getMessage());
            incrementStat("normalization_cache_error");
            return null;
        }
    }

    /**
     * 缓存标准化文本
     *
     * @param originalText 原始文本
     * @param normalizedText 标准化后的文本
     */
    public void putNormalizedText(String originalText, String normalizedText) {
        if (!isCacheEnabled() || originalText == null || normalizedText == null) {
            return;
        }

        try {
            redisCacheService.cacheNormalizedText(originalText, normalizedText);
            incrementStat("normalization_cache_put");
        } catch (Exception e) {
            log.warn("缓存标准化文本失败: {}", e.getMessage());
            incrementStat("normalization_cache_error");
        }
    }

    /**
     * 获取匹配结果缓存
     *
     * @param cacheKey 缓存键
     * @return 匹配结果列表，如果缓存中没有则返回null
     */
    public List<MatchResult> getMatchResults(String cacheKey) {
        if (!isCacheEnabled() || cacheKey == null) {
            return null;
        }

        try {
            List<MatchResult> results = redisCacheService.getCachedMatchResults(
                cacheKey, new TypeReference<List<MatchResult>>() {});

            if (results != null) {
                incrementStat("match_result_cache_hit");
            } else {
                incrementStat("match_result_cache_miss");
            }
            return results;
        } catch (Exception e) {
            log.warn("获取匹配结果缓存失败: {}", e.getMessage());
            incrementStat("match_result_cache_error");
            return null;
        }
    }

    /**
     * 缓存匹配结果
     *
     * @param cacheKey 缓存键
     * @param results 匹配结果列表
     */
    public void putMatchResults(String cacheKey, List<MatchResult> results) {
        if (!isCacheEnabled() || cacheKey == null || results == null) {
            return;
        }

        try {
            redisCacheService.cacheMatchResults(cacheKey, results);
            incrementStat("match_result_cache_put");
        } catch (Exception e) {
            log.warn("缓存匹配结果失败: {}", e.getMessage());
            incrementStat("match_result_cache_error");
        }
    }

    /**
     * 生成匹配结果缓存键
     *
     * @param rowData 行数据
     * @param threshold 阈值
     * @param maxResults 最大结果数
     * @return 缓存键
     */
    public String generateMatchCacheKey(Map<String, String> rowData, double threshold, int maxResults) {
        if (rowData == null) {
            return null;
        }

        // 简单的缓存键生成策略
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("match:");

        // 对行数据进行排序以确保一致性
        rowData.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> keyBuilder.append(entry.getKey())
                .append("=")
                .append(entry.getValue())
                .append(";"));

        keyBuilder.append("threshold=").append(threshold);
        keyBuilder.append("maxResults=").append(maxResults);

        return keyBuilder.toString();
    }

    /**
     * 清理所有缓存
     */
    public void clearAll() {
        if (!isCacheEnabled()) {
            return;
        }

        try {
            redisCacheService.clearKbCache();
            statistics.clear();
            log.info("所有缓存已清理");
        } catch (Exception e) {
            log.warn("清理缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息Map
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>(statistics);

        // 添加Redis状态信息
        stats.put("redis_enabled", properties.getCache().getEnableRedisCache());
        stats.put("redis_available", redisCacheService.isAvailable());
        stats.put("cache_type", "REDIS_ONLY");

        // 计算命中率
        long totalHits = statistics.getOrDefault("match_result_cache_hit", 0L) +
                        statistics.getOrDefault("normalization_cache_hit", 0L);
        long totalMisses = statistics.getOrDefault("match_result_cache_miss", 0L) +
                          statistics.getOrDefault("normalization_cache_miss", 0L);
        long totalRequests = totalHits + totalMisses;

        if (totalRequests > 0) {
            stats.put("hit_rate", (double) totalHits / totalRequests);
        } else {
            stats.put("hit_rate", 0.0);
        }

        stats.put("total_requests", totalRequests);
        stats.put("total_hits", totalHits);
        stats.put("total_misses", totalMisses);

        return stats;
    }

    /**
     * 判断缓存是否启用
     */
    private boolean isCacheEnabled() {
        return properties.getCache().getEnabled() &&
               properties.getCache().getEnableRedisCache() &&
               redisCacheService.isAvailable();
    }

    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        statistics.merge(key, 1L, Long::sum);
    }
}