package cn.savas.hub.module.algocell.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.algocell.dal.dataobject.kb.KbValueDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库要素值 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KbValueMapper extends BaseMapperX<KbValueDO> {

    /**
     * 查询所有启用的要素值
     *
     * @return 启用的要素值列表
     */
    default List<KbValueDO> selectEnabledValues() {
        return selectList(KbValueDO::getEnabled, true);
    }

    /**
     * 根据要素代码查询要素值
     *
     * @param featureCode 要素代码
     * @return 要素值列表
     */
    default List<KbValueDO> selectByFeatureCode(String featureCode) {
        return selectList(KbValueDO::getFeatureCode, featureCode);
    }

    /**
     * 根据要素代码和值代码查询要素值
     *
     * @param featureCode 要素代码
     * @param valueCode 值代码
     * @return 要素值对象
     */
    default KbValueDO selectByFeatureAndValueCode(String featureCode, String valueCode) {
        return selectOne(new LambdaQueryWrapper<>(KbValueDO.class)
            .eq(KbValueDO::getFeatureCode, featureCode)
            .eq(KbValueDO::getCode, valueCode));
    }

    /**
     * 查询指定要素的根节点（没有父节点的值）
     *
     * @param featureCode 要素代码
     * @return 根节点列表
     */
    default List<KbValueDO> selectRootValuesByFeature(String featureCode) {
        return selectList(new LambdaQueryWrapper<>(KbValueDO.class)
            .eq(KbValueDO::getFeatureCode, featureCode)
            .and(wrapper -> wrapper.isNull(KbValueDO::getParentCode).or().eq(KbValueDO::getParentCode, ""))
            .eq(KbValueDO::getEnabled, true)
            .orderByAsc(KbValueDO::getSortOrder, KbValueDO::getId));
    }

    /**
     * 查询指定父节点的子节点
     *
     * @param featureCode 要素代码
     * @param parentCode 父节点代码
     * @return 子节点列表
     */
    default List<KbValueDO> selectChildrenByParent(String featureCode, String parentCode) {
        return selectList(new LambdaQueryWrapper<>(KbValueDO.class)
            .eq(KbValueDO::getFeatureCode, featureCode)
            .eq(KbValueDO::getParentCode, parentCode)
            .eq(KbValueDO::getEnabled, true)
            .orderByAsc(KbValueDO::getSortOrder, KbValueDO::getId));
    }

    /**
     * 查询所有要素值，按要素和层级排序
     *
     * @return 排序后的要素值列表
     */
    default List<KbValueDO> selectAllOrderByFeatureAndLevel() {
        return selectList(new LambdaQueryWrapper<>(KbValueDO.class)
            .eq(KbValueDO::getEnabled, true)
            .orderByAsc(KbValueDO::getFeatureCode, KbValueDO::getLevel, KbValueDO::getSortOrder, KbValueDO::getId));
    }
}
