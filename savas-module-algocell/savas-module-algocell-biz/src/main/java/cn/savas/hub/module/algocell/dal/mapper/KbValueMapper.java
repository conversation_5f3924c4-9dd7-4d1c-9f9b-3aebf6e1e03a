package cn.savas.hub.module.algocell.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.algocell.dal.dataobject.kb.KbValueDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库要素值 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface KbValueMapper extends BaseMapperX<KbValueDO> {
    
    /**
     * 查询所有启用的要素值
     * 
     * @return 启用的要素值列表
     */
    default List<KbValueDO> selectEnabledValues() {
        return selectList(KbValueDO::getEnabled, true);
    }
    
    /**
     * 根据要素代码查询要素值
     * 
     * @param featureCode 要素代码
     * @return 要素值列表
     */
    default List<KbValueDO> selectByFeatureCode(String featureCode) {
        return selectList(KbValueDO::getFeatureCode, featureCode);
    }
    
    /**
     * 根据要素代码和值代码查询要素值
     * 
     * @param featureCode 要素代码
     * @param valueCode 值代码
     * @return 要素值对象
     */
    default KbValueDO selectByFeatureAndValueCode(String featureCode, String valueCode) {
        return selectOne(queryWrapper -> queryWrapper
            .eq("feature_code", featureCode)
            .eq("code", valueCode));
    }
    
    /**
     * 查询指定要素的根节点（没有父节点的值）
     * 
     * @param featureCode 要素代码
     * @return 根节点列表
     */
    default List<KbValueDO> selectRootValuesByFeature(String featureCode) {
        return selectList(queryWrapper -> queryWrapper
            .eq("feature_code", featureCode)
            .and(wrapper -> wrapper.isNull("parent_code").or().eq("parent_code", ""))
            .eq("enabled", true)
            .orderByAsc("sort_order", "id"));
    }
    
    /**
     * 查询指定父节点的子节点
     * 
     * @param featureCode 要素代码
     * @param parentCode 父节点代码
     * @return 子节点列表
     */
    default List<KbValueDO> selectChildrenByParent(String featureCode, String parentCode) {
        return selectList(queryWrapper -> queryWrapper
            .eq("feature_code", featureCode)
            .eq("parent_code", parentCode)
            .eq("enabled", true)
            .orderByAsc("sort_order", "id"));
    }
    
    /**
     * 查询所有要素值，按要素和层级排序
     * 
     * @return 排序后的要素值列表
     */
    default List<KbValueDO> selectAllOrderByFeatureAndLevel() {
        return selectList(queryWrapper -> queryWrapper
            .eq("enabled", true)
            .orderByAsc("feature_code", "level", "sort_order", "id"));
    }
}
