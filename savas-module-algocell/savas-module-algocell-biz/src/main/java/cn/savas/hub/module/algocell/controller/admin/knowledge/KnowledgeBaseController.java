package cn.savas.hub.module.algocell.controller.admin.knowledge;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.module.algocell.service.kb.KnowledgeBaseService;
import cn.savas.hub.module.algocell.controller.admin.knowledge.vo.KbVersionRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * 知识库管理控制器
 *
 * 提供知识库版本管理、热更新等API接口
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 知识库管理")
@RestController
@RequestMapping("/algocell/kb")
@Validated
@Slf4j
public class KnowledgeBaseController {

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @GetMapping("/version")
    @Operation(summary = "获取知识库版本信息")
    public CommonResult<KbVersionRespVO> getVersion() {
        KbVersionRespVO version = knowledgeBaseService.getVersion();
        return success(version);
    }

    @PostMapping("/reload")
    @Operation(summary = "重新加载知识库")
    public CommonResult<Boolean> reload() {
        log.info("收到重新加载知识库请求");

        boolean success = knowledgeBaseService.reload();

        log.info("知识库重新加载{}", success ? "成功" : "失败");

        return success(success);
    }

    @GetMapping("/check-updates")
    @Operation(summary = "检查知识库更新")
    public CommonResult<Boolean> checkForUpdates() {
        boolean hasUpdates = knowledgeBaseService.checkForUpdates();
        return success(hasUpdates);
    }

    @GetMapping("/status")
    @Operation(summary = "获取知识库状态")
    public CommonResult<String> getStatus() {
        String status = knowledgeBaseService.getStatus();
        return success(status);
    }

    @PostMapping("/clear-cache")
    @Operation(summary = "清理知识库缓存")
    public CommonResult<Boolean> clearCache() {
        log.info("收到清理知识库缓存请求");

        boolean success = knowledgeBaseService.clearCache();

        log.info("知识库缓存清理{}", success ? "成功" : "失败");

        return success(success);
    }

    @GetMapping("/memory-usage")
    @Operation(summary = "获取知识库内存使用情况")
    public CommonResult<Double> getMemoryUsage() {
        double memoryUsage = knowledgeBaseService.getMemoryUsage();
        return success(memoryUsage);
    }
}
