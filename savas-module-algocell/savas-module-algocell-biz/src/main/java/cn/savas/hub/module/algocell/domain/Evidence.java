package cn.savas.hub.module.algocell.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 匹配证据领域对象
 *
 * 记录匹配过程中发现的证据信息，用于可解释性分析
 * 包括命中的术语、位置、来源等详细信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Evidence implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要素代码
     * 例如：material、pressure、valve_type
     */
    private String featureCode;

    /**
     * 要素值代码
     * 匹配到的要素值
     */
    private String valueCode;

    /**
     * 源术语
     * 原始匹配到的术语文本
     */
    private String sourceTerm;

    /**
     * 列名
     * 命中术语所在的数据列名
     */
    private String colName;

    /**
     * 开始位置
     * 在文档中的起始位置
     */
    private Integer start;

    /**
     * 结束位置
     * 在文档中的结束位置
     */
    private Integer end;

    /**
     * 匹配来源
     * STRING：字符串匹配
     * REGEX：正则表达式匹配
     */
    private String from;

    /**
     * 置信度
     * 匹配的置信度分数，范围0.0-1.0
     */
    private Double confidence;

    /**
     * 原始文本
     * 命中位置的原始文本内容
     */
    private String originalText;

    /**
     * 获取要素值唯一键
     *
     * @return featureCode::valueCode格式的唯一键
     */
    public String getFeatureValueKey() {
        return featureCode + "::" + valueCode;
    }

    /**
     * 判断是否为字符串匹配
     *
     * @return 如果来源是STRING则返回true
     */
    public boolean isStringMatch() {
        return "STRING".equals(from);
    }

    /**
     * 判断是否为正则匹配
     *
     * @return 如果来源是REGEX则返回true
     */
    public boolean isRegexMatch() {
        return "REGEX".equals(from);
    }

    /**
     * 获取安全的置信度
     *
     * @return 如果置信度为null则返回1.0
     */
    public double getSafeConfidence() {
        return confidence != null ? confidence : 1.0;
    }

    /**
     * 获取匹配长度
     *
     * @return 结束位置减去开始位置，如果位置信息不完整则返回0
     */
    public int getMatchLength() {
        if (start != null && end != null && end >= start) {
            return end - start;
        }
        return 0;
    }

    /**
     * 判断位置信息是否有效
     *
     * @return 如果开始和结束位置都不为null且合理则返回true
     */
    public boolean hasValidPosition() {
        return start != null && end != null && end >= start;
    }
}
