package cn.savas.hub.module.algocell.controller.admin.match.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 批量匹配请求VO
 *
 * 用于接收多行数据的批量匹配请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchBatchReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量行数据
     * 每个元素代表一行数据的匹配请求
     */
    @NotEmpty(message = "批量数据不能为空")
    @Size(max = 100, message = "批量处理行数不能超过100行")
    @Valid
    private List<MatchRowReqVO> rows;

    /**
     * 批次标识
     * 用于标识本次批量处理的唯一ID
     */
    private String batchId;

    /**
     * 是否并行处理
     * 控制是否使用多线程并行处理
     * 默认为true
     */
    @Builder.Default
    private Boolean parallel = true;

    /**
     * 超时时间（秒）
     * 整个批量处理的超时时间
     * 默认为300秒（5分钟）
     */
    @Builder.Default
    private Integer timeoutSeconds = 300;

    /**
     * 失败策略
     * FAIL_FAST：遇到错误立即停止
     * CONTINUE：忽略错误继续处理
     * 默认为CONTINUE
     */
    @Builder.Default
    private String failureStrategy = "CONTINUE";

    /**
     * 获取行数
     *
     * @return 批量数据的行数
     */
    public int getRowCount() {
        return rows != null ? rows.size() : 0;
    }

    /**
     * 判断是否并行处理
     *
     * @return 如果parallel为null或true则返回true
     */
    public boolean shouldParallel() {
        return !Boolean.FALSE.equals(parallel);
    }

    /**
     * 获取安全的超时时间
     *
     * @return 如果超时时间为null则返回300秒
     */
    public int getSafeTimeoutSeconds() {
        return timeoutSeconds != null ? timeoutSeconds : 300;
    }

    /**
     * 判断是否快速失败策略
     *
     * @return 如果失败策略为FAIL_FAST则返回true
     */
    public boolean isFailFastStrategy() {
        return "FAIL_FAST".equals(failureStrategy);
    }
}
