package cn.savas.hub.module.algocell.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 文本标准化工具类
 * 
 * 提供文本预处理功能，包括全角转半角、繁简转换、标点统一等
 * 确保匹配的一致性和准确性
 * 
 * <AUTHOR>
 */
@Slf4j
public class TextNormalizer {
    
    /**
     * 全角到半角的字符映射
     */
    private static final Map<Character, Character> FULL_TO_HALF_MAP = new HashMap<>();
    
    /**
     * 标点符号统一映射
     */
    private static final Map<Character, Character> PUNCTUATION_MAP = new HashMap<>();
    
    /**
     * 多空白字符的正则模式
     */
    private static final Pattern MULTIPLE_SPACES = Pattern.compile("[\\u3000\\s]+");
    
    /**
     * 工业代码保护模式（保持大写）
     */
    private static final Pattern INDUSTRIAL_CODE_PATTERN = Pattern.compile("\\b[A-Z]+\\d+[A-Z]*\\b");
    
    static {
        // 初始化全角到半角映射
        initFullToHalfMap();
        
        // 初始化标点符号映射
        initPunctuationMap();
    }
    
    /**
     * 初始化全角到半角字符映射
     */
    private static void initFullToHalfMap() {
        // 数字 0-9
        for (int i = 0; i <= 9; i++) {
            FULL_TO_HALF_MAP.put((char) ('０' + i), (char) ('0' + i));
        }
        
        // 大写字母 A-Z
        for (int i = 0; i <= 25; i++) {
            FULL_TO_HALF_MAP.put((char) ('Ａ' + i), (char) ('A' + i));
        }
        
        // 小写字母 a-z
        for (int i = 0; i <= 25; i++) {
            FULL_TO_HALF_MAP.put((char) ('ａ' + i), (char) ('a' + i));
        }
        
        // 常用符号
        FULL_TO_HALF_MAP.put('　', ' ');  // 全角空格
        FULL_TO_HALF_MAP.put('（', '(');
        FULL_TO_HALF_MAP.put('）', ')');
        FULL_TO_HALF_MAP.put('［', '[');
        FULL_TO_HALF_MAP.put('］', ']');
        FULL_TO_HALF_MAP.put('｛', '{');
        FULL_TO_HALF_MAP.put('｝', '}');
        FULL_TO_HALF_MAP.put('：', ':');
        FULL_TO_HALF_MAP.put('；', ';');
        FULL_TO_HALF_MAP.put('，', ',');
        FULL_TO_HALF_MAP.put('．', '.');
        FULL_TO_HALF_MAP.put('？', '?');
        FULL_TO_HALF_MAP.put('！', '!');
        FULL_TO_HALF_MAP.put('｜', '|');
        FULL_TO_HALF_MAP.put('＋', '+');
        FULL_TO_HALF_MAP.put('－', '-');
        FULL_TO_HALF_MAP.put('＊', '*');
        FULL_TO_HALF_MAP.put('／', '/');
        FULL_TO_HALF_MAP.put('＝', '=');
    }
    
    /**
     * 初始化标点符号统一映射
     */
    private static void initPunctuationMap() {
        // 统一分隔符
        PUNCTUATION_MAP.put('｜', '|');
        PUNCTUATION_MAP.put('∫', '|');  // 某些情况下的特殊符号
        PUNCTUATION_MAP.put('∮', '|');
        
        // 统一括号
        PUNCTUATION_MAP.put('（', '(');
        PUNCTUATION_MAP.put('）', ')');
        
        // 统一逗号和分号
        PUNCTUATION_MAP.put('，', ',');
        PUNCTUATION_MAP.put('；', ';');
    }
    
    /**
     * 标准化文本
     * 
     * 执行完整的文本标准化流程：
     * 1. 全角转半角
     * 2. 标点符号统一
     * 3. 多空白合并
     * 4. 工业代码保护
     * 
     * @param text 原始文本
     * @return 标准化后的文本
     */
    public static String normalize(String text) {
        if (text == null) {
            return "";
        }
        
        try {
            // 1. 全角转半角
            String result = convertFullToHalf(text);
            
            // 2. 标点符号统一
            result = unifyPunctuation(result);
            
            // 3. 多空白合并为单个空格
            result = MULTIPLE_SPACES.matcher(result).replaceAll(" ");
            
            // 4. 去除首尾空白
            result = result.trim();
            
            log.debug("文本标准化: '{}' -> '{}'", text, result);
            return result;
            
        } catch (Exception e) {
            log.warn("文本标准化失败: {}", text, e);
            return text; // 失败时返回原文本
        }
    }
    
    /**
     * 全角字符转半角
     * 
     * @param text 输入文本
     * @return 转换后的文本
     */
    public static String convertFullToHalf(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        StringBuilder sb = new StringBuilder(text.length());
        for (char c : text.toCharArray()) {
            Character halfChar = FULL_TO_HALF_MAP.get(c);
            sb.append(halfChar != null ? halfChar : c);
        }
        
        return sb.toString();
    }
    
    /**
     * 统一标点符号
     * 
     * @param text 输入文本
     * @return 统一后的文本
     */
    public static String unifyPunctuation(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        StringBuilder sb = new StringBuilder(text.length());
        for (char c : text.toCharArray()) {
            Character unifiedChar = PUNCTUATION_MAP.get(c);
            sb.append(unifiedChar != null ? unifiedChar : c);
        }
        
        return sb.toString();
    }
    
    /**
     * 保护工业代码的大小写
     * 
     * 对于工业标准代码（如Q345R、CL150、A105等），保持其原有的大小写格式
     * 
     * @param text 输入文本
     * @return 处理后的文本
     */
    public static String preserveIndustrialCodes(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // 这里可以根据需要实现更复杂的工业代码识别逻辑
        // 目前简单保持原样
        return text;
    }
    
    /**
     * 清理特殊字符
     * 
     * 移除或替换可能影响匹配的特殊字符
     * 
     * @param text 输入文本
     * @return 清理后的文本
     */
    public static String cleanSpecialChars(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // 移除零宽字符和控制字符
        return text.replaceAll("[\\u200B-\\u200D\\uFEFF\\u0000-\\u001F\\u007F-\\u009F]", "");
    }
    
    /**
     * 单位标准化
     * 
     * 统一常见的单位表示方式
     * 
     * @param text 输入文本
     * @return 标准化后的文本
     */
    public static String normalizeUnits(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String result = text;
        
        // 英寸单位统一
        result = result.replaceAll("英寸|inch|inches|\"", "in");
        
        // 压力等级统一
        result = result.replaceAll("Class\\s*", "CL");
        result = result.replaceAll("class\\s*", "CL");
        
        // 毫米单位统一
        result = result.replaceAll("毫米|millimeter|millimeters", "mm");
        
        return result;
    }
    
    /**
     * 快速标准化（仅基本处理）
     * 
     * 用于性能敏感的场景，只执行最基本的标准化
     * 
     * @param text 输入文本
     * @return 标准化后的文本
     */
    public static String fastNormalize(String text) {
        if (text == null) {
            return "";
        }
        
        // 只进行空白处理和去除首尾空格
        return MULTIPLE_SPACES.matcher(text).replaceAll(" ").trim();
    }
}
