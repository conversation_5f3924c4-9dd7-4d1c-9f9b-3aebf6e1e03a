package cn.savas.hub.module.algocell.dal.dataobject.kb;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 知识库要素值表 DO
 *
 * 对应数据库表 kb_value
 * 存储要素值信息，支持层级关系，如材质->碳钢->Q345R
 *
 * <AUTHOR>
 */
@TableName("algocell_kb_value")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbValueDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 要素代码
     * 外键关联 kb_feature.code
     */
    private String featureCode;

    /**
     * 要素值代码
     * 归一化值，如：carbon_steel, Q345R, CL150
     */
    private String code;

    /**
     * 要素值名称
     * 显示名，如：碳钢, Q345R, CL150
     */
    private String name;

    /**
     * 父值代码
     * 用于构建层级关系，如Q345R的父为carbon_steel
     */
    private String parentCode;

    /**
     * 层级深度
     * 根节点为0，子节点依次递增
     */
    private Integer level;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 备注信息
     */
    private String remark;
}
