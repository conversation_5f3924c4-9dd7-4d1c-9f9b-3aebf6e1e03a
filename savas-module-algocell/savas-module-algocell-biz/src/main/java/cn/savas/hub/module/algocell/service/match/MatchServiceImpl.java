package cn.savas.hub.module.algocell.service.match;

import cn.savas.hub.module.algocell.domain.Indicator;
import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchBatchReqVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchResultRespVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchRowReqVO;
import cn.savas.hub.module.algocell.service.cache.MatchCacheService;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import cn.savas.hub.module.algocell.service.engine.MatchEngine;
import cn.savas.hub.module.algocell.service.kb.KbLoaderService;
import cn.savas.hub.module.algocell.service.kb.KbManagerService;
import cn.savas.hub.module.algocell.service.monitor.PerformanceMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 匹配服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MatchServiceImpl implements MatchService {

    @Resource
    private MatchEngine matchEngine;

    @Resource
    private KbManagerService kbManagerService;

    @Resource
    private KbLoaderService kbLoaderService;

    @Resource
    private MatchCacheService matchCacheService;

    @Resource
    private PerformanceMonitorService performanceMonitorService;

    /**
     * 匹配统计信息
     */
    private final AtomicLong matchCount = new AtomicLong(0);
    private final AtomicLong totalDuration = new AtomicLong(0);
    private final Map<String, Object> statistics = new ConcurrentHashMap<>();

    @Override
    public MatchResultRespVO matchRow(MatchRowReqVO request) {
        long startTime = System.currentTimeMillis();

        try {
            // 获取知识库运行时
            KbRuntime kbRuntime = kbManagerService.getKbRuntime();
            if (kbRuntime == null) {
                throw new RuntimeException("知识库未就绪");
            }

            // 执行匹配
            List<MatchResult> matchResults = matchRowData(
                request.getRowData(),
                request.getSafeThreshold(),
                request.getSafeMaxResults()
            );

            // 构建响应
            MatchResultRespVO response = buildMatchResponse(matchResults, request);

            // 更新统计信息
            updateStatistics(startTime);

            return response;

        } catch (Exception e) {
            log.error("行匹配失败", e);
            throw new RuntimeException("匹配失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<MatchResultRespVO> matchBatch(MatchBatchReqVO request) {
        long startTime = System.currentTimeMillis();

        try {
            List<MatchRowReqVO> rows = request.getRows();
            List<MatchResultRespVO> results = new ArrayList<>();

            if (request.shouldParallel() && rows.size() > 1) {
                // 并行处理
                List<CompletableFuture<MatchResultRespVO>> futures = rows.stream()
                    .map(row -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return matchRow(row);
                        } catch (Exception e) {
                            log.error("批量匹配中单行处理失败", e);
                            if (request.isFailFastStrategy()) {
                                throw new RuntimeException(e);
                            }
                            return createErrorResponse(e.getMessage());
                        }
                    }))
                    .collect(Collectors.toList());

                // 等待所有任务完成
                for (CompletableFuture<MatchResultRespVO> future : futures) {
                    results.add(future.get());
                }
            } else {
                // 串行处理
                for (MatchRowReqVO row : rows) {
                    try {
                        results.add(matchRow(row));
                    } catch (Exception e) {
                        log.error("批量匹配中单行处理失败", e);
                        if (request.isFailFastStrategy()) {
                            throw e;
                        }
                        results.add(createErrorResponse(e.getMessage()));
                    }
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("批量匹配完成，处理行数: {}, 耗时: {}ms", rows.size(), duration);

            return results;

        } catch (Exception e) {
            log.error("批量匹配失败", e);
            throw new RuntimeException("批量匹配失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<MatchResult> matchRowData(Map<String, String> rowData, double threshold, int maxResults) {
        if (rowData == null || rowData.isEmpty()) {
            return Collections.emptyList();
        }

        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            // 尝试从缓存获取结果
            String cacheKey = matchCacheService.generateMatchCacheKey(rowData, threshold, maxResults);
            List<MatchResult> cachedResults = matchCacheService.getMatchResults(cacheKey);
            if (cachedResults != null) {
                success = true;
                return cachedResults;
            }

            // 获取知识库运行时
            KbRuntime kbRuntime = kbManagerService.getKbRuntime();
            if (kbRuntime == null) {
                throw new RuntimeException("知识库未就绪");
            }

            // 获取所有指标
            List<Indicator> indicators = kbLoaderService.loadIndicators();

            // 执行匹配
            List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

            // 过滤和限制结果
            List<MatchResult> filteredResults = results.stream()
                .filter(result -> result.getSafeMatchRate() >= threshold)
                .limit(maxResults)
                .collect(Collectors.toList());

            // 缓存结果
            if (cacheKey != null) {
                matchCacheService.putMatchResults(cacheKey, filteredResults);
            }

            success = true;
            return filteredResults;

        } finally {
            // 记录性能监控
            long duration = System.currentTimeMillis() - startTime;
            performanceMonitorService.recordMatch("matchRowData", duration, success);
        }
    }

    @Override
    public List<String> getIndicatorRequirements(Long indicatorId) {
        KbRuntime kbRuntime = kbManagerService.getKbRuntime();
        if (kbRuntime == null) {
            return Collections.emptyList();
        }

        return kbRuntime.getIndicatorRequirements(indicatorId).stream()
            .map(req -> {
                String desc = req.getFeatureCode();
                if (req.getRequiredValueCode() != null) {
                    desc += "=" + req.getRequiredValueCode();
                } else {
                    desc += "(ANY)";
                }
                desc += " [" + req.getSafeMatchMode().getCode() + "]";
                if (req.getSafeWeight() != 1.0) {
                    desc += " (权重:" + req.getSafeWeight() + ")";
                }
                return desc;
            })
            .collect(Collectors.toList());
    }

    @Override
    public boolean warmUp() {
        try {
            log.info("开始预热匹配引擎...");

            // 确保知识库已加载
            KbRuntime kbRuntime = kbManagerService.getKbRuntime();
            if (kbRuntime == null) {
                kbManagerService.reloadKnowledgeBase();
                kbRuntime = kbManagerService.getKbRuntime();
            }

            if (kbRuntime == null) {
                log.error("知识库加载失败，预热失败");
                return false;
            }

            // 执行一次测试匹配
            Map<String, String> testData = new HashMap<>();
            testData.put("name", "测试阀门");
            testData.put("material", "Q345R");
            testData.put("pressure", "CL150");

            matchRowData(testData, 0.0, 10);

            log.info("匹配引擎预热完成");
            return true;

        } catch (Exception e) {
            log.error("匹配引擎预热失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getMatchStatistics() {
        Map<String, Object> stats = new HashMap<>(statistics);
        stats.put("matchCount", matchCount.get());
        stats.put("totalDuration", totalDuration.get());

        long count = matchCount.get();
        if (count > 0) {
            stats.put("averageDuration", totalDuration.get() / (double) count);
        } else {
            stats.put("averageDuration", 0.0);
        }

        // 添加知识库统计信息
        KbRuntime kbRuntime = kbManagerService.getKbRuntime();
        if (kbRuntime != null) {
            stats.putAll(kbRuntime.getStatistics());
        }

        // 添加缓存统计信息
        stats.putAll(matchCacheService.getCacheStatistics());

        // 添加性能监控统计信息
        stats.putAll(performanceMonitorService.getPerformanceStatistics());

        return stats;
    }

    /**
     * 构建匹配响应
     */
    private MatchResultRespVO buildMatchResponse(List<MatchResult> matchResults, MatchRowReqVO request) {
        List<MatchResultRespVO.IndicatorMatchVO> allMatches = new ArrayList<>();
        List<MatchResultRespVO.IndicatorMatchVO> autoBindings = new ArrayList<>();
        List<MatchResultRespVO.IndicatorMatchVO> candidates = new ArrayList<>();

        for (MatchResult result : matchResults) {
            MatchResultRespVO.IndicatorMatchVO matchVO = MatchResultRespVO.IndicatorMatchVO.builder()
                .indicatorId(result.getIndicatorId())
                .indicatorCode(result.getIndicatorCode())
                .indicatorName(result.getIndicatorName())
                .matchRate(result.getMatchRate())
                .score(result.getScore())
                .missingFeatures(result.getMissingFeatures())
                .conflictInfo(result.getConflictInfo())
                .build();

            // 根据请求决定是否包含证据
            if (request.shouldIncludeEvidence()) {
                matchVO.setEvidences(result.getEvidences());
            }

            // 设置状态和描述
            if (result.canAutoBinding()) {
                matchVO.setStatus("AUTO");
                matchVO.setDescription("100%匹配，自动绑定");
                autoBindings.add(matchVO);
            } else if (result.isHighQualityMatch()) {
                matchVO.setStatus("PENDING");
                matchVO.setDescription("高质量匹配，建议人工确认");
                candidates.add(matchVO);
            } else {
                matchVO.setStatus("PENDING");
                matchVO.setDescription("部分匹配，需要人工审核");
            }

            allMatches.add(matchVO);
        }

        return MatchResultRespVO.builder()
            .matches(allMatches)
            .autoBindings(autoBindings)
            .candidates(candidates)
            .processTime(LocalDateTime.now())
            .totalMatches(matchResults.size())
            .kbVersion(kbManagerService.getKbVersion())
            .build();
    }

    /**
     * 创建错误响应
     */
    private MatchResultRespVO createErrorResponse(String errorMessage) {
        return MatchResultRespVO.builder()
            .matches(Collections.emptyList())
            .autoBindings(Collections.emptyList())
            .candidates(Collections.emptyList())
            .processTime(LocalDateTime.now())
            .totalMatches(0)
            .build();
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics(long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        matchCount.incrementAndGet();
        totalDuration.addAndGet(duration);
    }
}
