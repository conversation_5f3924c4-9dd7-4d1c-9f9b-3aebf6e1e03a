package cn.savas.hub.module.algocell.dal.dataobject.kb;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 指标表 DO
 *
 * 对应数据库表 indicator
 * 存储业务指标信息
 *
 * <AUTHOR>
 */
@TableName("algocell_indicator")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 指标代码
     * 业务唯一标识，如：2-5001
     */
    private String code;

    /**
     * 指标名称
     * 业务描述，如：化工、炼油 中低压阀门DN15~DN50
     */
    private String name;

    /**
     * 指标描述
     * 详细说明信息
     */
    private String description;

    /**
     * 指标分类
     * 用于业务分类管理
     */
    private String category;

    /**
     * 排序权重
     * 用于相同匹配率时的排序，数值越大优先级越高
     */
    private Integer sortWeight;

    /**
     * 是否启用
     * 控制指标是否参与匹配
     */
    private Boolean enabled;
}
