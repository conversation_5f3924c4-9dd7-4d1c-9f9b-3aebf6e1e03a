package cn.savas.hub.module.algocell.domain;

import cn.savas.hub.module.algocell.enums.TermTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 术语条目领域对象
 *
 * 表示知识库中的术语到要素值的映射关系
 * 支持字符串精确匹配和正则表达式模式匹配
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TermEntry implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要素代码
     * 例如：material、pressure、valve_type
     */
    private String featureCode;

    /**
     * 要素值代码
     * 命中该术语时映射到的要素值
     */
    private String valueCode;

    /**
     * 术语类型
     * STRING：字符串精确匹配
     * REGEX：正则表达式匹配
     */
    private TermTypeEnum type;

    /**
     * 表达式内容
     * 对于STRING类型：具体的词汇或短语，如"Q345R"、"阀门"
     * 对于REGEX类型：正则表达式，如"(20G|Q235|Q345R|A105)"
     */
    private String expr;

    /**
     * 正则标志位
     * 用于Pattern.compile的flags参数
     * 例如：Pattern.CASE_INSENSITIVE = 2
     */
    private Integer flags;

    /**
     * 获取要素值的唯一键
     *
     * @return featureCode::valueCode格式的唯一键
     */
    public String getFeatureValueKey() {
        return featureCode + "::" + valueCode;
    }

    /**
     * 判断是否为字符串类型术语
     *
     * @return 如果是字符串类型返回true
     */
    public boolean isStringType() {
        return TermTypeEnum.STRING.equals(type);
    }

    /**
     * 判断是否为正则类型术语
     *
     * @return 如果是正则类型返回true
     */
    public boolean isRegexType() {
        return TermTypeEnum.REGEX.equals(type);
    }

    /**
     * 获取安全的flags值
     *
     * @return 如果flags为null则返回0，否则返回原值
     */
    public int getSafeFlags() {
        return flags != null ? flags : 0;
    }
}
