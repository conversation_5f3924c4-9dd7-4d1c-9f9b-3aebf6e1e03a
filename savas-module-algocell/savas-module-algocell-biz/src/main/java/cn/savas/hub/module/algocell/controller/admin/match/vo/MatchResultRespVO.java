package cn.savas.hub.module.algocell.controller.admin.match.vo;

import cn.savas.hub.module.algocell.domain.Evidence;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 匹配结果响应VO
 *
 * 返回单行数据的匹配结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchResultRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 匹配结果列表
     * 按匹配率和得分排序的指标匹配结果
     */
    @Builder.Default
    private List<IndicatorMatchVO> matches = new ArrayList<>();

    /**
     * 自动绑定的指标
     * 100%匹配的指标，可以直接自动绑定
     */
    @Builder.Default
    private List<IndicatorMatchVO> autoBindings = new ArrayList<>();

    /**
     * 候选指标
     * 80%~99%匹配的指标，需要人工确认
     */
    @Builder.Default
    private List<IndicatorMatchVO> candidates = new ArrayList<>();

    /**
     * 处理时间
     * 匹配处理的时间戳
     */
    private LocalDateTime processTime;

    /**
     * 处理耗时（毫秒）
     * 本次匹配的执行时间
     */
    private Long processDuration;

    /**
     * 总匹配数
     * 找到的匹配结果总数
     */
    private Integer totalMatches;

    /**
     * 知识库版本
     * 使用的知识库版本号
     */
    private String kbVersion;

    /**
     * 指标匹配详情VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndicatorMatchVO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 指标ID
         */
        private Long indicatorId;

        /**
         * 指标代码
         */
        private String indicatorCode;

        /**
         * 指标名称
         */
        private String indicatorName;

        /**
         * 匹配率
         * 范围：0.0-1.0
         */
        private Double matchRate;

        /**
         * 细粒度得分
         */
        private Double score;

        /**
         * 缺失要素列表
         */
        @Builder.Default
        private List<String> missingFeatures = new ArrayList<>();

        /**
         * 匹配证据列表
         */
        @Builder.Default
        private List<Evidence> evidences = new ArrayList<>();

        /**
         * 匹配状态
         * AUTO：自动确认
         * PENDING：待确认
         */
        private String status;

        /**
         * 冲突信息
         */
        private String conflictInfo;

        /**
         * 匹配说明
         * 人性化的匹配结果说明
         */
        private String description;
    }

    /**
     * 获取自动绑定数量
     *
     * @return 自动绑定指标的数量
     */
    public int getAutoBindingCount() {
        return autoBindings != null ? autoBindings.size() : 0;
    }

    /**
     * 获取候选数量
     *
     * @return 候选指标的数量
     */
    public int getCandidateCount() {
        return candidates != null ? candidates.size() : 0;
    }

    /**
     * 判断是否有自动绑定结果
     *
     * @return 如果有自动绑定结果则返回true
     */
    public boolean hasAutoBindings() {
        return getAutoBindingCount() > 0;
    }

    /**
     * 判断是否有候选结果
     *
     * @return 如果有候选结果则返回true
     */
    public boolean hasCandidates() {
        return getCandidateCount() > 0;
    }
}
