package cn.savas.hub.module.algocell.controller.admin.match.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

/**
 * 行级匹配请求VO
 *
 * 用于接收单行数据的匹配请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchRowReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行数据
     * 键为列名，值为列值
     * 例如：{"name": "阀门", "material": "Q345R", "standard": "CL150"}
     */
    @NotEmpty(message = "行数据不能为空")
    @Size(max = 50, message = "列数不能超过50个")
    private Map<String, String> rowData;

    /**
     * 匹配阈值
     * 只返回匹配率大于等于该阈值的结果
     * 默认为0.0，返回所有结果
     */
    @Builder.Default
    private Double threshold = 0.0;

    /**
     * 最大返回数量
     * 限制返回的匹配结果数量，避免结果过多
     * 默认为20
     */
    @Builder.Default
    private Integer maxResults = 20;

    /**
     * 是否包含证据
     * 控制是否在结果中包含详细的匹配证据信息
     * 默认为true
     */
    @Builder.Default
    private Boolean includeEvidence = true;

    /**
     * 是否启用冲突检测
     * 控制是否检测同一要素的多值冲突
     * 默认为true
     */
    @Builder.Default
    private Boolean enableConflictDetection = true;

    /**
     * 业务标识
     * 用于标识请求来源或业务场景
     */
    private String businessId;

    /**
     * 获取安全的阈值
     *
     * @return 如果阈值为null则返回0.0
     */
    public double getSafeThreshold() {
        return threshold != null ? threshold : 0.0;
    }

    /**
     * 获取安全的最大结果数
     *
     * @return 如果最大结果数为null则返回20
     */
    public int getSafeMaxResults() {
        return maxResults != null ? maxResults : 20;
    }

    /**
     * 判断是否包含证据
     *
     * @return 如果includeEvidence为null或true则返回true
     */
    public boolean shouldIncludeEvidence() {
        return !Boolean.FALSE.equals(includeEvidence);
    }

    /**
     * 判断是否启用冲突检测
     *
     * @return 如果enableConflictDetection为null或true则返回true
     */
    public boolean shouldEnableConflictDetection() {
        return !Boolean.FALSE.equals(enableConflictDetection);
    }
}
