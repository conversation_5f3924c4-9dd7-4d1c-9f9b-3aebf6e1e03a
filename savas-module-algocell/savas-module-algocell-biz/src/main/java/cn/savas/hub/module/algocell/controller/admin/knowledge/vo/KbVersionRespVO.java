package cn.savas.hub.module.algocell.controller.admin.knowledge.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库版本响应VO
 *
 * 返回知识库版本信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbVersionRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前版本号
     */
    private String currentVersion;

    /**
     * 版本发布时间
     */
    private LocalDateTime releaseTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 要素数量
     */
    private Integer featureCount;

    /**
     * 要素值数量
     */
    private Integer valueCount;

    /**
     * 术语数量
     */
    private Integer termCount;

    /**
     * 指标数量
     */
    private Integer indicatorCount;

    /**
     * 字符串术语数量
     */
    private Integer stringTermCount;

    /**
     * 正则术语数量
     */
    private Integer regexTermCount;

    /**
     * 知识库状态
     * LOADING：加载中
     * READY：就绪
     * ERROR：错误
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 内存使用情况（MB）
     */
    private Double memoryUsageMB;

    /**
     * 判断知识库是否就绪
     *
     * @return 如果状态为READY则返回true
     */
    public boolean isReady() {
        return "READY".equals(status);
    }

    /**
     * 判断知识库是否正在加载
     *
     * @return 如果状态为LOADING则返回true
     */
    public boolean isLoading() {
        return "LOADING".equals(status);
    }

    /**
     * 判断知识库是否有错误
     *
     * @return 如果状态为ERROR则返回true
     */
    public boolean hasError() {
        return "ERROR".equals(status);
    }
}
