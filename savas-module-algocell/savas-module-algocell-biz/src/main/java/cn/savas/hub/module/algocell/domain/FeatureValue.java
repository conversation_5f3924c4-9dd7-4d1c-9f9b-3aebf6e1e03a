package cn.savas.hub.module.algocell.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 要素值领域对象
 *
 * 表示知识库中的概念-值对，支持层级结构
 * 例如：材质->碳钢->Q345R 的层级关系
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeatureValue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要素代码
     * 例如：material（材质）、pressure（压力）、valve_type（阀门类型）
     */
    private String featureCode;

    /**
     * 要素值代码
     * 归一化后的值，例如：carbon_steel、Q345R、CL150
     */
    private String valueCode;

    /**
     * 显示名称
     * 用于展示的友好名称，例如：碳钢、Q345R、CL150
     */
    private String displayName;

    /**
     * 父值代码
     * 用于构建层级关系，例如Q345R的父值是carbon_steel
     * 如果为null表示这是顶级值
     */
    private String parentValueCode;

    /**
     * 获取要素值的唯一键
     * 格式：featureCode::valueCode
     *
     * @return 唯一键字符串
     */
    public String getUniqueKey() {
        return featureCode + "::" + valueCode;
    }

    /**
     * 判断是否为根节点（顶级要素值）
     *
     * @return 如果没有父值则返回true
     */
    public boolean isRoot() {
        return parentValueCode == null || parentValueCode.trim().isEmpty();
    }

    /**
     * 获取父值的唯一键
     *
     * @return 父值的唯一键，如果没有父值则返回null
     */
    public String getParentUniqueKey() {
        if (isRoot()) {
            return null;
        }
        return featureCode + "::" + parentValueCode;
    }
}
