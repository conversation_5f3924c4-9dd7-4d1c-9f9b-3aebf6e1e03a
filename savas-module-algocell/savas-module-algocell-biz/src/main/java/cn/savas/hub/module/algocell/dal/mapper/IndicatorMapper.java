package cn.savas.hub.module.algocell.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.algocell.dal.dataobject.kb.IndicatorDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 指标 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface IndicatorMapper extends BaseMapperX<IndicatorDO> {
    
    /**
     * 查询所有启用的指标
     * 
     * @return 启用的指标列表
     */
    default List<IndicatorDO> selectEnabledIndicators() {
        return selectList(IndicatorDO::getEnabled, true);
    }
    
    /**
     * 根据指标代码查询指标
     * 
     * @param code 指标代码
     * @return 指标对象
     */
    default IndicatorDO selectByCode(String code) {
        return selectOne(IndicatorDO::getCode, code);
    }
    
    /**
     * 根据指标ID集合查询指标
     * 
     * @param indicatorIds 指标ID集合
     * @return 指标列表
     */
    default List<IndicatorDO> selectByIds(Set<Long> indicatorIds) {
        if (indicatorIds == null || indicatorIds.isEmpty()) {
            return List.of();
        }
        return selectBatchIds(indicatorIds);
    }
    
    /**
     * 根据分类查询指标
     * 
     * @param category 指标分类
     * @return 指标列表
     */
    default List<IndicatorDO> selectByCategory(String category) {
        return selectList(queryWrapper -> queryWrapper
            .eq("category", category)
            .eq("enabled", true)
            .orderByDesc("sort_weight", "id"));
    }
    
    /**
     * 查询所有指标，按权重排序
     * 
     * @return 排序后的指标列表
     */
    default List<IndicatorDO> selectAllOrderBySortWeight() {
        return selectList(queryWrapper -> queryWrapper
            .eq("enabled", true)
            .orderByDesc("sort_weight", "id"));
    }
    
    /**
     * 根据名称模糊查询指标
     * 
     * @param name 指标名称关键字
     * @return 匹配的指标列表
     */
    default List<IndicatorDO> selectByNameLike(String name) {
        return selectList(queryWrapper -> queryWrapper
            .like("name", name)
            .eq("enabled", true)
            .orderByDesc("sort_weight", "id"));
    }
}
