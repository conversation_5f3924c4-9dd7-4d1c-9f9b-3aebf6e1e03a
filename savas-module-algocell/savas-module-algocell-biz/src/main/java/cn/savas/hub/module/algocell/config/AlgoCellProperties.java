package cn.savas.hub.module.algocell.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 知识库匹配系统配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "savas.algocell")
public class AlgoCellProperties {
    
    /**
     * 匹配配置
     */
    private MatchConfig match = new MatchConfig();
    
    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();
    
    /**
     * 性能配置
     */
    private PerformanceConfig performance = new PerformanceConfig();
    
    /**
     * 知识库配置
     */
    private KnowledgeBaseConfig kb = new KnowledgeBaseConfig();
    
    @Data
    public static class MatchConfig {
        /**
         * 默认匹配阈值
         */
        private Double defaultThreshold = 0.0;
        
        /**
         * 默认最大返回结果数
         */
        private Integer defaultMaxResults = 20;
        
        /**
         * 自动绑定阈值
         */
        private Double autoBindingThreshold = 1.0;
        
        /**
         * 高质量匹配阈值
         */
        private Double highQualityThreshold = 0.8;
        
        /**
         * 是否启用冲突检测
         */
        private Boolean enableConflictDetection = true;
        
        /**
         * 匹配超时时间（毫秒）
         */
        private Long timeoutMs = 30000L;
        
        /**
         * 正则匹配最大执行时间（毫秒）
         */
        private Long regexTimeoutMs = 1000L;
    }
    
    @Data
    public static class CacheConfig {
        /**
         * 是否启用缓存
         */
        private Boolean enabled = true;

        /**
         * 是否启用Redis缓存
         */
        private Boolean enableRedisCache = true;

        /**
         * Redis缓存键前缀
         */
        private String redisKeyPrefix = "KB:";

        /**
         * Redis缓存过期时间（小时）
         */
        private Integer redisExpireHours = 24;
    }
    
    @Data
    public static class PerformanceConfig {
        /**
         * 是否启用并行处理
         */
        private Boolean enableParallel = true;
        
        /**
         * 线程池核心线程数
         */
        private Integer corePoolSize = 4;
        
        /**
         * 线程池最大线程数
         */
        private Integer maxPoolSize = 8;
        
        /**
         * 线程池队列大小
         */
        private Integer queueCapacity = 100;
        
        /**
         * 批量处理阈值
         */
        private Integer batchThreshold = 10;
        
        /**
         * 是否启用性能监控
         */
        private Boolean enableMonitoring = true;
        
        /**
         * 慢查询阈值（毫秒）
         */
        private Long slowQueryThreshold = 1000L;
    }
    
    @Data
    public static class KnowledgeBaseConfig {
        /**
         * 是否启用自动重载
         */
        private Boolean enableAutoReload = false;
        
        /**
         * 自动重载检查间隔（分钟）
         */
        private Integer autoReloadIntervalMinutes = 30;
        
        /**
         * 是否启用预热
         */
        private Boolean enableWarmup = true;
        
        /**
         * 预热延迟时间（秒）
         */
        private Integer warmupDelaySeconds = 10;
        
        /**
         * 最大术语数量限制
         */
        private Integer maxTermCount = 100000;
        
        /**
         * 最大正则表达式数量限制
         */
        private Integer maxRegexCount = 1000;
        
        /**
         * 是否启用术语优先级
         */
        private Boolean enableTermPriority = true;
    }
}
