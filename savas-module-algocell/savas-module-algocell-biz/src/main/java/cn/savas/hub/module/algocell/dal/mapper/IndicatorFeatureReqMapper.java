package cn.savas.hub.module.algocell.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.algocell.dal.dataobject.kb.IndicatorFeatureReqDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 指标要素要求 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface IndicatorFeatureReqMapper extends BaseMapperX<IndicatorFeatureReqDO> {
    
    /**
     * 根据指标ID查询要素要求
     * 
     * @param indicatorId 指标ID
     * @return 要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectByIndicatorId(Long indicatorId) {
        return selectList(queryWrapper -> queryWrapper
            .eq("indicator_id", indicatorId)
            .orderByAsc("sort_order", "id"));
    }
    
    /**
     * 根据指标ID集合查询要素要求
     * 
     * @param indicatorIds 指标ID集合
     * @return 要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectByIndicatorIds(Set<Long> indicatorIds) {
        if (indicatorIds == null || indicatorIds.isEmpty()) {
            return List.of();
        }
        return selectList(queryWrapper -> queryWrapper
            .in("indicator_id", indicatorIds)
            .orderByAsc("indicator_id", "sort_order", "id"));
    }
    
    /**
     * 根据要素代码查询要素要求
     * 
     * @param featureCode 要素代码
     * @return 要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectByFeatureCode(String featureCode) {
        return selectList(queryWrapper -> queryWrapper
            .eq("feature_code", featureCode)
            .orderByAsc("indicator_id", "sort_order", "id"));
    }
    
    /**
     * 根据要素代码和值代码查询要素要求
     * 
     * @param featureCode 要素代码
     * @param valueCode 值代码
     * @return 要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectByFeatureAndValueCode(String featureCode, String valueCode) {
        return selectList(queryWrapper -> queryWrapper
            .eq("feature_code", featureCode)
            .eq("required_value_code", valueCode)
            .orderByAsc("indicator_id", "sort_order", "id"));
    }
    
    /**
     * 查询必需的要素要求
     * 
     * @param indicatorId 指标ID
     * @return 必需的要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectRequiredByIndicatorId(Long indicatorId) {
        return selectList(queryWrapper -> queryWrapper
            .eq("indicator_id", indicatorId)
            .eq("required", true)
            .orderByAsc("sort_order", "id"));
    }
    
    /**
     * 查询所有要素要求
     * 
     * @return 所有要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectAll() {
        return selectList(queryWrapper -> queryWrapper
            .orderByAsc("indicator_id", "sort_order", "id"));
    }
    
    /**
     * 根据匹配模式查询要素要求
     * 
     * @param matchMode 匹配模式
     * @return 要素要求列表
     */
    default List<IndicatorFeatureReqDO> selectByMatchMode(String matchMode) {
        return selectList(queryWrapper -> queryWrapper
            .eq("match_mode", matchMode)
            .orderByAsc("indicator_id", "sort_order", "id"));
    }
}
