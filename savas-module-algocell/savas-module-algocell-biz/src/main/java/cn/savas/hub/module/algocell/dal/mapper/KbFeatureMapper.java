package cn.savas.hub.module.algocell.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.algocell.dal.dataobject.kb.KbFeatureDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库要素 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface KbFeatureMapper extends BaseMapperX<KbFeatureDO> {
    
    /**
     * 查询所有启用的要素
     * 
     * @return 启用的要素列表
     */
    default List<KbFeatureDO> selectEnabledFeatures() {
        return selectList(KbFeatureDO::getEnabled, true);
    }
    
    /**
     * 根据代码查询要素
     * 
     * @param code 要素代码
     * @return 要素对象
     */
    default KbFeatureDO selectByCode(String code) {
        return selectOne(KbFeatureDO::getCode, code);
    }
    
    /**
     * 查询所有要素，按排序权重排序
     * 
     * @return 排序后的要素列表
     */
    default List<KbFeatureDO> selectAllOrderBySortOrder() {
        return selectList(queryWrapper -> queryWrapper.orderByDesc("sort_order", "id"));
    }
}
