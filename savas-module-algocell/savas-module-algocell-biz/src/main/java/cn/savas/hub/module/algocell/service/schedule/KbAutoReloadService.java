package cn.savas.hub.module.algocell.service.schedule;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import cn.savas.hub.module.algocell.service.kb.KbManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 知识库自动重载服务
 * 
 * 定期检查知识库更新并自动重载
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class KbAutoReloadService {
    
    @Resource
    private AlgoCellProperties properties;
    
    @Resource
    private KbManagerService kbManagerService;
    
    /**
     * 是否已初始化
     */
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    
    /**
     * 最后检查时间
     */
    private volatile LocalDateTime lastCheckTime;
    
    /**
     * 最后重载时间
     */
    private volatile LocalDateTime lastReloadTime;
    
    /**
     * 应用启动完成后的初始化
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        if (!properties.getKb().getEnableAutoReload()) {
            log.info("知识库自动重载功能已禁用");
            return;
        }
        
        log.info("知识库自动重载服务启动，检查间隔: {}分钟", 
                properties.getKb().getAutoReloadIntervalMinutes());
        
        initialized.set(true);
        lastCheckTime = LocalDateTime.now();
    }
    
    /**
     * 定期检查知识库更新
     * 
     * 每分钟执行一次，根据配置的间隔进行实际检查
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkForUpdates() {
        if (!isAutoReloadEnabled() || !initialized.get()) {
            return;
        }
        
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 检查是否到了检查时间
            if (lastCheckTime != null) {
                long minutesSinceLastCheck = java.time.Duration.between(lastCheckTime, now).toMinutes();
                if (minutesSinceLastCheck < properties.getKb().getAutoReloadIntervalMinutes()) {
                    return; // 还没到检查时间
                }
            }
            
            lastCheckTime = now;
            
            log.debug("开始检查知识库更新...");
            
            // 检查是否有更新
            boolean hasUpdates = kbManagerService.checkForUpdates();
            
            if (hasUpdates) {
                log.info("检测到知识库更新，开始自动重载...");
                
                boolean reloadSuccess = kbManagerService.reloadKnowledgeBase();
                
                if (reloadSuccess) {
                    lastReloadTime = now;
                    log.info("知识库自动重载成功");
                } else {
                    log.error("知识库自动重载失败");
                }
            } else {
                log.debug("知识库无更新");
            }
            
        } catch (Exception e) {
            log.error("知识库自动重载检查失败", e);
        }
    }
    
    /**
     * 手动触发检查更新
     * 
     * @return 是否有更新并成功重载
     */
    public boolean triggerCheckAndReload() {
        if (!isAutoReloadEnabled()) {
            log.warn("自动重载功能未启用，无法手动触发");
            return false;
        }
        
        try {
            log.info("手动触发知识库更新检查...");
            
            boolean hasUpdates = kbManagerService.checkForUpdates();
            
            if (hasUpdates) {
                log.info("检测到知识库更新，开始重载...");
                
                boolean reloadSuccess = kbManagerService.reloadKnowledgeBase();
                
                if (reloadSuccess) {
                    lastReloadTime = LocalDateTime.now();
                    lastCheckTime = LocalDateTime.now();
                    log.info("手动触发的知识库重载成功");
                    return true;
                } else {
                    log.error("手动触发的知识库重载失败");
                    return false;
                }
            } else {
                log.info("知识库无更新");
                lastCheckTime = LocalDateTime.now();
                return false;
            }
            
        } catch (Exception e) {
            log.error("手动触发知识库更新检查失败", e);
            return false;
        }
    }
    
    /**
     * 获取自动重载状态信息
     * 
     * @return 状态信息
     */
    public AutoReloadStatus getStatus() {
        AutoReloadStatus status = new AutoReloadStatus();
        status.enabled = isAutoReloadEnabled();
        status.intervalMinutes = properties.getKb().getAutoReloadIntervalMinutes();
        status.lastCheckTime = lastCheckTime;
        status.lastReloadTime = lastReloadTime;
        status.initialized = initialized.get();
        
        if (lastCheckTime != null) {
            long minutesSinceLastCheck = java.time.Duration.between(lastCheckTime, LocalDateTime.now()).toMinutes();
            status.minutesSinceLastCheck = minutesSinceLastCheck;
            status.nextCheckInMinutes = Math.max(0, properties.getKb().getAutoReloadIntervalMinutes() - minutesSinceLastCheck);
        }
        
        return status;
    }
    
    /**
     * 判断自动重载是否启用
     */
    private boolean isAutoReloadEnabled() {
        return properties.getKb().getEnableAutoReload();
    }
    
    /**
     * 自动重载状态类
     */
    public static class AutoReloadStatus {
        public boolean enabled;
        public int intervalMinutes;
        public LocalDateTime lastCheckTime;
        public LocalDateTime lastReloadTime;
        public boolean initialized;
        public long minutesSinceLastCheck;
        public long nextCheckInMinutes;
    }
}
