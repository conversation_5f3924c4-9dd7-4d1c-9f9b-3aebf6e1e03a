package cn.savas.hub.module.algocell.domain;

import cn.savas.hub.module.algocell.enums.MatchModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 指标要素要求领域对象
 *
 * 定义指标对特定要素的要求，包括要求的值和匹配模式
 * 例如：材质=碳钢(ANCESTOR_OK)、压力=CL150(EXACT)
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorFeatureReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 指标ID
     * 关联的指标标识
     */
    private Long indicatorId;

    /**
     * 要素代码
     * 例如：material、pressure、valve_type
     */
    private String featureCode;

    /**
     * 要求的要素值代码
     * 如果为null表示仅要求该要素存在，不限制具体值
     */
    private String requiredValueCode;

    /**
     * 匹配模式
     * EXACT：精确匹配
     * ANCESTOR_OK：祖先匹配（命中子值也算匹配）
     * DESCENDANT_OK：后代匹配（命中父值也算匹配）
     * ANY：任意匹配（该要素出现即可）
     */
    private MatchModeEnum matchMode;

    /**
     * 要素权重
     * 用于计算匹配得分，默认为1.0
     */
    private Double weight;

    /**
     * 是否必需
     * 标识该要素是否为必需项
     */
    private Boolean required;

    /**
     * 获取要求的要素值唯一键
     *
     * @return featureCode::requiredValueCode格式，如果requiredValueCode为null则只返回featureCode
     */
    public String getRequiredFeatureValueKey() {
        if (requiredValueCode == null || requiredValueCode.trim().isEmpty()) {
            return featureCode;
        }
        return featureCode + "::" + requiredValueCode;
    }

    /**
     * 判断是否仅要求要素存在
     *
     * @return 如果requiredValueCode为null或ANY模式则返回true
     */
    public boolean isAnyValueAcceptable() {
        return requiredValueCode == null
            || requiredValueCode.trim().isEmpty()
            || MatchModeEnum.ANY.equals(matchMode);
    }

    /**
     * 获取安全的权重值
     *
     * @return 如果权重为null则返回1.0
     */
    public double getSafeWeight() {
        return weight != null ? weight : 1.0;
    }

    /**
     * 判断是否为必需要素
     *
     * @return 如果required为null或true则返回true
     */
    public boolean isRequired() {
        return !Boolean.FALSE.equals(required);
    }

    /**
     * 获取安全的匹配模式
     *
     * @return 如果matchMode为null则返回EXACT
     */
    public MatchModeEnum getSafeMatchMode() {
        return matchMode != null ? matchMode : MatchModeEnum.EXACT;
    }
}
