package cn.savas.hub.module.algocell.dal.dataobject.kb;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 知识库要素表 DO
 *
 * 对应数据库表 kb_feature
 * 存储概念/要素信息，如材质、压力、阀门类型等
 *
 * <AUTHOR>
 */
@TableName("algocell_kb_feature")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbFeatureDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 要素代码
     * 唯一标识，如：material, pressure, valve_type
     */
    private String code;

    /**
     * 要素名称
     * 显示名称，如：材质, 压力, 阀门类型
     */
    private String name;

    /**
     * 要素描述
     */
    private String description;

    /**
     * 排序权重
     * 用于显示排序，数值越大越靠前
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;
}
