package cn.savas.hub.module.algocell.service.cache;

import cn.savas.hub.module.algocell.config.AlgoCellProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Redis缓存服务测试类
 * 
 * <AUTHOR>
 */
class RedisCacheServiceTest {
    
    @Mock
    private AlgoCellProperties properties;
    
    @Mock
    private AlgoCellProperties.CacheConfig cacheConfig;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private ValueOperations<String, Object> valueOperations;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private RedisHealthService redisHealthService;
    
    @InjectMocks
    private RedisCacheService redisCacheService;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Mock配置
        when(properties.getCache()).thenReturn(cacheConfig);
        when(cacheConfig.getEnabled()).thenReturn(true);
        when(cacheConfig.getEnableRedisCache()).thenReturn(true);
        when(cacheConfig.getRedisKeyPrefix()).thenReturn("KB:");
        when(cacheConfig.getRedisExpireHours()).thenReturn(24);
        
        // Mock Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(redisHealthService.isRedisAvailable()).thenReturn(true);
    }
    
    @Test
    void testCacheKbVersion() {
        // 准备测试数据
        String version = "KB_20231201_001";
        
        // 执行测试
        redisCacheService.cacheKbVersion(version);
        
        // 验证调用
        verify(valueOperations).set(eq("KB:VERSION"), eq(version), eq(24), eq(TimeUnit.HOURS));
    }
    
    @Test
    void testGetCachedKbVersion() {
        // 准备测试数据
        String expectedVersion = "KB_20231201_001";
        when(valueOperations.get("KB:VERSION")).thenReturn(expectedVersion);
        
        // 执行测试
        String actualVersion = redisCacheService.getCachedKbVersion();
        
        // 验证结果
        assertEquals(expectedVersion, actualVersion);
        verify(valueOperations).get("KB:VERSION");
    }
    
    @Test
    void testCacheFeatureWeights() throws Exception {
        // 准备测试数据
        Map<String, Double> featureWeights = new HashMap<>();
        featureWeights.put("material", 1.2);
        featureWeights.put("pressure", 1.5);
        
        String expectedJson = "{\"material\":1.2,\"pressure\":1.5}";
        when(objectMapper.writeValueAsString(featureWeights)).thenReturn(expectedJson);
        
        // 执行测试
        redisCacheService.cacheFeatureWeights(featureWeights);
        
        // 验证调用
        verify(objectMapper).writeValueAsString(featureWeights);
        verify(valueOperations).set(eq("KB:FEATURE_WEIGHT"), eq(expectedJson), eq(24), eq(TimeUnit.HOURS));
    }
    
    @Test
    void testCacheValueToIndicators() throws Exception {
        // 准备测试数据
        String featureValueKey = "material::Q345R";
        Set<Long> indicatorIds = Set.of(1L, 2L, 3L);
        
        String expectedJson = "[1,2,3]";
        when(objectMapper.writeValueAsString(indicatorIds)).thenReturn(expectedJson);
        
        // 执行测试
        redisCacheService.cacheValueToIndicators(featureValueKey, indicatorIds);
        
        // 验证调用
        verify(objectMapper).writeValueAsString(indicatorIds);
        verify(valueOperations).set(eq("KB:VAL2IND:material::Q345R"), eq(expectedJson), eq(24), eq(TimeUnit.HOURS));
    }
    
    @Test
    void testClearKbCache() {
        // 准备测试数据
        Set<String> keys = Set.of("KB:VERSION", "KB:FEATURE_WEIGHT", "KB:VAL2IND:test");
        when(redisTemplate.keys("KB:*")).thenReturn(keys);
        
        // 执行测试
        redisCacheService.clearKbCache();
        
        // 验证调用
        verify(redisTemplate).keys("KB:*");
        verify(redisTemplate).delete(keys);
    }
    
    @Test
    void testHasKbVersionUpdate() {
        // 准备测试数据
        String currentVersion = "KB_20231201_001";
        String cachedVersion = "KB_20231201_002";
        when(valueOperations.get("KB:VERSION")).thenReturn(cachedVersion);
        
        // 执行测试
        boolean hasUpdate = redisCacheService.hasKbVersionUpdate(currentVersion);
        
        // 验证结果
        assertTrue(hasUpdate);
    }
    
    @Test
    void testHasKbVersionUpdateSameVersion() {
        // 准备测试数据
        String currentVersion = "KB_20231201_001";
        String cachedVersion = "KB_20231201_001";
        when(valueOperations.get("KB:VERSION")).thenReturn(cachedVersion);
        
        // 执行测试
        boolean hasUpdate = redisCacheService.hasKbVersionUpdate(currentVersion);
        
        // 验证结果
        assertFalse(hasUpdate);
    }
    
    @Test
    void testRedisNotAvailable() {
        // Mock Redis不可用
        when(redisHealthService.isRedisAvailable()).thenReturn(false);
        
        // 执行测试
        redisCacheService.cacheKbVersion("test");
        
        // 验证没有调用Redis操作
        verify(valueOperations, never()).set(anyString(), any(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testRedisDisabled() {
        // Mock Redis功能禁用
        when(cacheConfig.getEnabled()).thenReturn(false);
        
        // 执行测试
        redisCacheService.cacheKbVersion("test");
        
        // 验证没有调用Redis操作
        verify(valueOperations, never()).set(anyString(), any(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testIsAvailable() {
        // 测试可用状态
        assertTrue(redisCacheService.isAvailable());
        
        // 测试不可用状态
        when(redisHealthService.isRedisAvailable()).thenReturn(false);
        assertFalse(redisCacheService.isAvailable());
    }
}
