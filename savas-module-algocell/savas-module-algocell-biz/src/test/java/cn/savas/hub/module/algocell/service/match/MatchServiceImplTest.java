package cn.savas.hub.module.algocell.service.match;

import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchResultRespVO;
import cn.savas.hub.module.algocell.controller.admin.match.vo.MatchRowReqVO;
import cn.savas.hub.module.algocell.domain.MatchResult;
import cn.savas.hub.module.algocell.service.cache.MatchCacheService;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import cn.savas.hub.module.algocell.service.engine.MatchEngine;
import cn.savas.hub.module.algocell.service.kb.KbLoaderService;
import cn.savas.hub.module.algocell.service.kb.KbManagerService;
import cn.savas.hub.module.algocell.service.monitor.PerformanceMonitorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 匹配服务实现测试类
 *
 * <AUTHOR>
 */
class MatchServiceImplTest {

    @Mock
    private MatchEngine matchEngine;

    @Mock
    private KbManagerService kbManagerService;

    @Mock
    private KbLoaderService kbLoaderService;

    @Mock
    private MatchCacheService matchCacheService;

    @Mock
    private PerformanceMonitorService performanceMonitorService;

    @InjectMocks
    private MatchServiceImpl matchService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testMatchRowSuccess() {
        // 准备测试数据
        MatchRowReqVO request = MatchRowReqVO.builder()
            .rowData(Map.of("name", "阀门", "material", "Q345R"))
            .threshold(0.0)
            .maxResults(10)
            .includeEvidence(true)
            .build();

        // Mock依赖
        KbRuntime mockKbRuntime = new KbRuntime();
        when(kbManagerService.getKbRuntime()).thenReturn(mockKbRuntime);
        when(kbManagerService.getKbVersion()).thenReturn("TEST_VERSION");
        when(matchCacheService.generateMatchCacheKey(any(), anyDouble(), anyInt())).thenReturn("test_key");
        when(matchCacheService.getMatchResults(anyString())).thenReturn(null);
        when(kbLoaderService.loadIndicators()).thenReturn(java.util.Collections.emptyList());
        when(matchEngine.matchRow(any(), any(), any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        MatchResultRespVO result = matchService.matchRow(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getProcessTime());
        assertEquals("TEST_VERSION", result.getKbVersion());
        assertEquals(0, result.getTotalMatches());

        // 验证方法调用
        verify(kbManagerService).getKbRuntime();
        verify(matchCacheService).generateMatchCacheKey(any(), anyDouble(), anyInt());
        verify(matchCacheService).getMatchResults(anyString());
        verify(performanceMonitorService).recordMatch(eq("matchRowData"), anyLong(), eq(true));
    }

    @Test
    void testMatchRowWithCache() {
        // 准备测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        MatchRowReqVO request = MatchRowReqVO.builder()
            .rowData(rowData)
            .build();

        // Mock缓存命中
        when(kbManagerService.getKbRuntime()).thenReturn(new KbRuntime());
        when(matchCacheService.generateMatchCacheKey(any(), anyDouble(), anyInt())).thenReturn("test_key");
        when(matchCacheService.getMatchResults(anyString())).thenReturn(java.util.Collections.emptyList());
        when(kbManagerService.getKbVersion()).thenReturn("TEST_VERSION");

        // 执行测试
        MatchResultRespVO result = matchService.matchRow(request);

        // 验证结果
        assertNotNull(result);

        // 验证没有调用匹配引擎（因为缓存命中）
        verify(matchEngine, never()).matchRow(any(), any(), any());
        verify(performanceMonitorService).recordMatch(eq("matchRowData"), anyLong(), eq(true));
    }

    @Test
    void testMatchRowKbNotReady() {
        // 准备测试数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        MatchRowReqVO request = MatchRowReqVO.builder()
            .rowData(rowData)
            .build();

        // Mock知识库未就绪
        when(kbManagerService.getKbRuntime()).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            matchService.matchRow(request);
        });

        assertTrue(exception.getMessage().contains("知识库未就绪"));
        verify(performanceMonitorService).recordMatch(eq("matchRowData"), anyLong(), eq(false));
    }

    @Test
    void testWarmUpSuccess() {
        // Mock依赖
        when(kbManagerService.getKbRuntime()).thenReturn(new KbRuntime());
        when(kbLoaderService.loadIndicators()).thenReturn(java.util.Collections.emptyList());
        when(matchEngine.matchRow(any(), any(), any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        boolean result = matchService.warmUp();

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(kbManagerService, atLeastOnce()).getKbRuntime();
    }

    @Test
    void testWarmUpWithReload() {
        // Mock知识库初始为null，重载后可用
        when(kbManagerService.getKbRuntime())
            .thenReturn(null)
            .thenReturn(new KbRuntime());
        when(kbManagerService.reloadKnowledgeBase()).thenReturn(true);
        when(kbLoaderService.loadIndicators()).thenReturn(java.util.Collections.emptyList());
        when(matchEngine.matchRow(any(), any(), any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        boolean result = matchService.warmUp();

        // 验证结果
        assertTrue(result);

        // 验证重载被调用
        verify(kbManagerService).reloadKnowledgeBase();
    }

    @Test
    void testGetMatchStatistics() {
        // Mock依赖
        KbRuntime mockKbRuntime = new KbRuntime();
        when(kbManagerService.getKbRuntime()).thenReturn(mockKbRuntime);
        Map<String, Object> mockStats = new HashMap<>();
        mockStats.put("matchCount", 100);
        when(matchCacheService.getCacheStatistics()).thenReturn(mockStats);
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("avg_duration", 100.0);
        when(performanceMonitorService.getPerformanceStatistics()).thenReturn(statistics);

        // 执行测试
        Map<String, Object> stats = matchService.getMatchStatistics();

        // 验证结果
        assertNotNull(stats);
        assertTrue(stats.containsKey("matchCount"));
        assertTrue(stats.containsKey("totalDuration"));
        assertTrue(stats.containsKey("averageDuration"));
        assertTrue(stats.containsKey("cache_hits"));
        assertTrue(stats.containsKey("avg_duration"));

        // 验证方法调用
        verify(matchCacheService).getCacheStatistics();
        verify(performanceMonitorService).getPerformanceStatistics();
    }

    @Test
    void testGetIndicatorRequirements() {
        // Mock依赖
        KbRuntime mockKbRuntime = new KbRuntime();
        when(kbManagerService.getKbRuntime()).thenReturn(mockKbRuntime);

        // 执行测试
        java.util.List<String> requirements = matchService.getIndicatorRequirements(1L);

        // 验证结果
        assertNotNull(requirements);

        // 验证方法调用
        verify(kbManagerService).getKbRuntime();
    }

    @Test
    void testMatchRowDataEmptyInput() {
        // 测试空输入
        java.util.List<MatchResult> results =
            matchService.matchRowData(null, 0.0, 10);

        assertTrue(results.isEmpty());

        results = matchService.matchRowData(new HashMap<>(), 0.0, 10);
        assertTrue(results.isEmpty());
    }
}
