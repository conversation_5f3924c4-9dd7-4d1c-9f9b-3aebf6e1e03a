package cn.savas.hub.module.algocell.integration;

import cn.savas.hub.module.algocell.domain.*;
import cn.savas.hub.module.algocell.enums.MatchModeEnum;
import cn.savas.hub.module.algocell.enums.TermTypeEnum;
import cn.savas.hub.module.algocell.service.engine.KbRuntime;
import cn.savas.hub.module.algocell.service.engine.MatchEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 匹配系统集成测试
 *
 * 测试完整的匹配流程，验证系统的端到端功能
 *
 * <AUTHOR>
 */
@SpringBootTest
class MatchSystemIntegrationTest {

    private MatchEngine matchEngine;
    private KbRuntime kbRuntime;
    private List<Indicator> indicators;

    @BeforeEach
    void setUp() {
        matchEngine = new MatchEngine();
        kbRuntime = new KbRuntime();
        setupCompleteKnowledgeBase();
        indicators = setupCompleteIndicators();
    }

    /**
     * 设置完整的测试知识库
     */
    private void setupCompleteKnowledgeBase() {
        // 材质要素值
        addFeatureValue("material", "carbon_steel", "碳钢", null);
        addFeatureValue("material", "Q345R", "Q345R", "carbon_steel");
        addFeatureValue("material", "20G", "20G", "carbon_steel");
        addFeatureValue("material", "A105", "A105", "carbon_steel");
        addFeatureValue("material", "stainless_steel", "不锈钢", null);
        addFeatureValue("material", "316L", "316L", "stainless_steel");

        // 压力要素值
        addFeatureValue("pressure", "CL150", "CL150", null);
        addFeatureValue("pressure", "CL300", "CL300", null);
        addFeatureValue("pressure", "PN16", "PN16", null);

        // 阀门类型要素值
        addFeatureValue("valve_type", "valve", "阀门", null);
        addFeatureValue("valve_type", "gate_valve", "闸阀", "valve");
        addFeatureValue("valve_type", "ball_valve", "球阀", "valve");

        // 尺寸要素值
        addFeatureValue("size", "DN15", "DN15", null);
        addFeatureValue("size", "DN25", "DN25", null);
        addFeatureValue("size", "DN50", "DN50", null);

        // 字符串术语
        addStringTerm("material", "Q345R", "Q345R");
        addStringTerm("material", "20G", "20G");
        addStringTerm("material", "A105", "A105");
        addStringTerm("material", "316L", "316L");
        addStringTerm("pressure", "CL150", "CL150");
        addStringTerm("pressure", "CL300", "CL300");
        addStringTerm("pressure", "PN16", "PN16");
        addStringTerm("valve_type", "valve", "阀门");
        addStringTerm("valve_type", "gate_valve", "闸阀");
        addStringTerm("valve_type", "ball_valve", "球阀");
        addStringTerm("size", "DN15", "DN15");
        addStringTerm("size", "DN25", "DN25");
        addStringTerm("size", "DN50", "DN50");

        // 正则术语
        addRegexTerm("material", "carbon_steel", "(Q345R|20G|A105)");
        addRegexTerm("pressure", "CL150", "Class\\s*150|CL\\s*150");
        addRegexTerm("size", "DN15", "DN\\s*15|15mm");
    }

    /**
     * 设置完整的测试指标
     */
    private List<Indicator> setupCompleteIndicators() {
        List<Indicator> indicatorList = new ArrayList<>();

        // 指标1：化工中低压阀门DN15~DN50
        Indicator indicator1 = createIndicator(1L, "IND-001", "化工中低压阀门DN15~DN50");
        indicatorList.add(indicator1);
        addIndicatorRequirement(1L, "material", "carbon_steel", MatchModeEnum.ANCESTOR_OK, 1.2);
        addIndicatorRequirement(1L, "pressure", "CL150", MatchModeEnum.EXACT, 1.5);
        addIndicatorRequirement(1L, "valve_type", "valve", MatchModeEnum.EXACT, 1.0);
        addIndicatorRequirement(1L, "size", null, MatchModeEnum.ANY, 0.8);

        // 指标2：不锈钢球阀
        Indicator indicator2 = createIndicator(2L, "IND-002", "不锈钢球阀");
        indicatorList.add(indicator2);
        addIndicatorRequirement(2L, "material", "stainless_steel", MatchModeEnum.ANCESTOR_OK, 1.5);
        addIndicatorRequirement(2L, "valve_type", "ball_valve", MatchModeEnum.EXACT, 1.0);

        // 指标3：高压闸阀
        Indicator indicator3 = createIndicator(3L, "IND-003", "高压闸阀");
        indicatorList.add(indicator3);
        addIndicatorRequirement(3L, "pressure", "CL300", MatchModeEnum.EXACT, 1.5);
        addIndicatorRequirement(3L, "valve_type", "gate_valve", MatchModeEnum.EXACT, 1.0);

        return indicatorList;
    }

    @Test
    void testPerfectMatch() {
        // 测试完美匹配场景
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("pressure", "CL150");
        rowData.put("size", "DN25");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());

        // 应该完美匹配指标1
        MatchResult bestMatch = results.get(0);
        assertEquals(1L, bestMatch.getIndicatorId());
        assertEquals(1.0, bestMatch.getMatchRate(), 0.001);
        assertTrue(bestMatch.canAutoBinding());
        assertTrue(bestMatch.getMissingFeatures().isEmpty());

        // 验证证据
        assertFalse(bestMatch.getEvidences().isEmpty());
        Set<String> evidenceFeatures = new HashSet<>();
        for (Evidence evidence : bestMatch.getEvidences()) {
            evidenceFeatures.add(evidence.getFeatureCode());
        }
        assertTrue(evidenceFeatures.contains("material"));
        assertTrue(evidenceFeatures.contains("pressure"));
        assertTrue(evidenceFeatures.contains("valve_type"));
    }

    @Test
    void testMultipleMatches() {
        // 测试多个匹配场景
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "闸阀");
        rowData.put("material", "Q345R");
        rowData.put("pressure", "CL300");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());

        // 应该匹配多个指标
        assertTrue(results.size() >= 2);

        // 验证指标3（高压闸阀）应该有较高匹配率
        Optional<MatchResult> indicator3Match = results.stream()
            .filter(r -> r.getIndicatorId().equals(3L))
            .findFirst();
        assertTrue(indicator3Match.isPresent());
        assertTrue(indicator3Match.get().getMatchRate() > 0.8);
    }

    @Test
    void testRegexMatch() {
        // 测试正则匹配
        Map<String, String> rowData = new HashMap<>();
        rowData.put("description", "材质A105，压力Class 150的阀门");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());

        // 验证正则匹配的证据
        boolean hasRegexEvidence = false;
        for (MatchResult result : results) {
            for (Evidence evidence : result.getEvidences()) {
                if ("REGEX".equals(evidence.getFrom())) {
                    hasRegexEvidence = true;
                    break;
                }
            }
            if (hasRegexEvidence) break;
        }
        assertTrue(hasRegexEvidence);
    }

    @Test
    void testHierarchicalMatch() {
        // 测试层级匹配
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "球阀");
        rowData.put("material", "316L");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        // 应该匹配指标2（不锈钢球阀）
        Optional<MatchResult> indicator2Match = results.stream()
            .filter(r -> r.getIndicatorId().equals(2L))
            .findFirst();
        assertTrue(indicator2Match.isPresent());

        MatchResult match = indicator2Match.get();
        assertEquals(1.0, match.getMatchRate(), 0.001);

        // 验证316L匹配到不锈钢要求（层级匹配）
        boolean hasStainlessSteelMatch = match.getEvidences().stream()
            .anyMatch(e -> "material".equals(e.getFeatureCode()) && "316L".equals(e.getValueCode()));
        assertTrue(hasStainlessSteelMatch);
    }

    @Test
    void testComplexRealWorldData() {
        // 测试复杂的真实世界数据
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("standard", "闸阀,CL150,A105/13Cr*STL,BB,OS&Y,RF,API 602||英吋直径:1||管表号/压力等级:CL150");
        rowData.put("specification", "DN25, Class 150, 碳钢材质");

        List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);

        assertFalse(results.isEmpty());

        // 应该有高质量匹配
        MatchResult bestMatch = results.get(0);
        assertTrue(bestMatch.getMatchRate() >= 0.8);

        // 验证多个证据来源
        Set<String> evidenceSources = new HashSet<>();
        for (Evidence evidence : bestMatch.getEvidences()) {
            evidenceSources.add(evidence.getFrom());
        }
        assertTrue(evidenceSources.contains("STRING"));

        // 验证多列匹配
        Set<String> evidenceColumns = new HashSet<>();
        for (Evidence evidence : bestMatch.getEvidences()) {
            evidenceColumns.add(evidence.getColName());
        }
        assertTrue(evidenceColumns.size() > 1);
    }

    @Test
    void testPerformance() {
        // 性能测试
        Map<String, String> rowData = new HashMap<>();
        rowData.put("name", "阀门");
        rowData.put("material", "Q345R");
        rowData.put("pressure", "CL150");

        long startTime = System.currentTimeMillis();

        // 执行100次匹配
        for (int i = 0; i < 100; i++) {
            List<MatchResult> results = matchEngine.matchRow(rowData, kbRuntime, indicators);
            assertFalse(results.isEmpty());
        }

        long duration = System.currentTimeMillis() - startTime;

        // 平均每次匹配应该在合理时间内完成（这里设为50ms）
        double avgDuration = duration / 100.0;
        assertTrue(avgDuration < 50, "平均匹配时间过长: " + avgDuration + "ms");
    }

    // 辅助方法
    private void addFeatureValue(String featureCode, String valueCode, String displayName, String parentCode) {
        FeatureValue fv = FeatureValue.builder()
            .featureCode(featureCode)
            .valueCode(valueCode)
            .displayName(displayName)
            .parentValueCode(parentCode)
            .build();
        kbRuntime.addFeatureValue(fv);
    }

    private void addStringTerm(String featureCode, String valueCode, String expr) {
        TermEntry term = TermEntry.builder()
            .featureCode(featureCode)
            .valueCode(valueCode)
            .type(TermTypeEnum.STRING)
            .expr(expr)
            .build();
        kbRuntime.addTermEntry(term);
    }

    private void addRegexTerm(String featureCode, String valueCode, String expr) {
        TermEntry term = TermEntry.builder()
            .featureCode(featureCode)
            .valueCode(valueCode)
            .type(TermTypeEnum.REGEX)
            .expr(expr)
            .flags(0)
            .build();
        kbRuntime.addTermEntry(term);
    }

    private Indicator createIndicator(Long id, String code, String name) {
        return Indicator.builder()
            .id(id)
            .code(code)
            .name(name)
            .enabled(true)
            .build();
    }

    private void addIndicatorRequirement(Long indicatorId, String featureCode, String valueCode,
                                       MatchModeEnum mode, Double weight) {
        IndicatorFeatureReq req = IndicatorFeatureReq.builder()
            .indicatorId(indicatorId)
            .featureCode(featureCode)
            .requiredValueCode(valueCode)
            .matchMode(mode)
            .weight(weight)
            .required(true)
            .build();
        kbRuntime.addIndicatorRequirement(indicatorId, req);
    }
}
