package cn.savas.hub.module.algocell.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文本标准化工具测试类
 * 
 * <AUTHOR>
 */
class TextNormalizerTest {
    
    @Test
    void testNormalize() {
        // 测试基本标准化
        String input = "　　这是一个　测试　　";
        String expected = "这是一个 测试";
        assertEquals(expected, TextNormalizer.normalize(input));
    }
    
    @Test
    void testFullToHalf() {
        // 测试全角转半角
        String input = "Ｑ３４５Ｒ（ＣＬ１５０）";
        String expected = "Q345R(CL150)";
        assertEquals(expected, TextNormalizer.convertFullToHalf(input));
    }
    
    @Test
    void testFullToHalfNumbers() {
        // 测试全角数字转半角
        String input = "０１２３４５６７８９";
        String expected = "0123456789";
        assertEquals(expected, TextNormalizer.convertFullToHalf(input));
    }
    
    @Test
    void testFullToHalfLetters() {
        // 测试全角字母转半角
        String input = "ＡＢＣａｂｃ";
        String expected = "ABCabc";
        assertEquals(expected, TextNormalizer.convertFullToHalf(input));
    }
    
    @Test
    void testUnifyPunctuation() {
        // 测试标点符号统一
        String input = "材质：碳钢，压力；CL150｜阀门";
        String expected = "材质：碳钢,压力;CL150|阀门";
        assertEquals(expected, TextNormalizer.unifyPunctuation(input));
    }
    
    @Test
    void testComplexNormalization() {
        // 测试复杂标准化
        String input = "　　闸阀，ＣＬ１５０，Ａ１０５／１３Ｃｒ＊ＳＴＬ，ＢＢ，ＯＳ＆Ｙ，ＲＦ　　";
        String result = TextNormalizer.normalize(input);
        
        // 应该去除多余空白，转换全角字符
        assertFalse(result.startsWith(" "));
        assertFalse(result.endsWith(" "));
        assertTrue(result.contains("CL150"));
        assertTrue(result.contains("A105"));
        assertTrue(result.contains("OS&Y"));
    }
    
    @Test
    void testNormalizeUnits() {
        // 测试单位标准化
        String input1 = "1英寸阀门";
        String expected1 = "1in阀门";
        assertEquals(expected1, TextNormalizer.normalizeUnits(input1));
        
        String input2 = "Class 150压力等级";
        String expected2 = "CL150压力等级";
        assertEquals(expected2, TextNormalizer.normalizeUnits(input2));
        
        String input3 = "100毫米管径";
        String expected3 = "100mm管径";
        assertEquals(expected3, TextNormalizer.normalizeUnits(input3));
    }
    
    @Test
    void testCleanSpecialChars() {
        // 测试清理特殊字符
        String input = "正常文本\u200B零宽字符\uFEFF字节序标记";
        String result = TextNormalizer.cleanSpecialChars(input);
        assertEquals("正常文本零宽字符字节序标记", result);
    }
    
    @Test
    void testFastNormalize() {
        // 测试快速标准化
        String input = "　　多个　　空格　　";
        String expected = "多个 空格";
        assertEquals(expected, TextNormalizer.fastNormalize(input));
    }
    
    @Test
    void testNullAndEmpty() {
        // 测试null和空字符串
        assertEquals("", TextNormalizer.normalize(null));
        assertEquals("", TextNormalizer.normalize(""));
        
        assertNull(TextNormalizer.convertFullToHalf(null));
        assertEquals("", TextNormalizer.convertFullToHalf(""));
        
        assertNull(TextNormalizer.unifyPunctuation(null));
        assertEquals("", TextNormalizer.unifyPunctuation(""));
    }
    
    @Test
    void testIndustrialCodes() {
        // 测试工业代码保护
        String input = "Q345R材质CL150压力等级";
        String result = TextNormalizer.preserveIndustrialCodes(input);
        
        // 目前实现是保持原样
        assertEquals(input, result);
    }
    
    @Test
    void testChineseCharacters() {
        // 测试中文字符处理
        String input = "这是中文测试，包含标点符号。";
        String result = TextNormalizer.normalize(input);
        
        // 中文字符应该保持不变
        assertTrue(result.contains("这是中文测试"));
        assertTrue(result.contains("包含标点符号"));
    }
    
    @Test
    void testMixedContent() {
        // 测试混合内容
        String input = "ＡＰＩ　６０２标准的英寸直径：１英寸｜｜管表号／压力等级：ＣＬ１５０";
        String result = TextNormalizer.normalize(input);
        
        assertTrue(result.contains("API"));
        assertTrue(result.contains("602"));
        assertTrue(result.contains("CL150"));
        assertFalse(result.contains("　")); // 不应该包含全角空格
    }
}
