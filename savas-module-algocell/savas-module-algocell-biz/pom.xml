<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.savas.hub</groupId>
        <artifactId>savas-module-algocell</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>savas-module-algocell-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <dependencies>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-algocell-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-biz-ip</artifactId>
        </dependency>
        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-web</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- AOP 相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- Test 测试相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
