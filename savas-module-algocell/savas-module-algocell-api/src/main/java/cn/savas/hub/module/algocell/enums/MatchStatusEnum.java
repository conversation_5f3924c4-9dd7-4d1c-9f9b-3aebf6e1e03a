package cn.savas.hub.module.algocell.enums;

import lombok.Getter;

/**
 * 匹配状态枚举
 * 
 * 用于标识匹配结果的确认状态，支持自动绑定和人工确认流程
 * 
 * <AUTHOR>
 */
@Getter
public enum MatchStatusEnum {
    
    /**
     * 自动确认 - 100%匹配率的指标自动绑定
     * 满足所有要素要求的指标会被自动确认
     */
    AUTO("AUTO", "自动确认"),
    
    /**
     * 人工确认 - 需要人工审核的匹配结果
     * 80%~99%匹配率的候选指标需要人工确认
     */
    CONFIRMED("CONFIRMED", "人工确认"),
    
    /**
     * 待处理 - 初始状态，等待处理
     * 新产生的匹配结果的初始状态
     */
    PENDING("PENDING", "待处理"),
    
    /**
     * 已拒绝 - 人工审核后拒绝的匹配
     * 经过人工审核认为不合适的匹配结果
     */
    REJECTED("REJECTED", "已拒绝");
    
    /**
     * 枚举代码
     */
    private final String code;
    
    /**
     * 枚举名称
     */
    private final String name;
    
    MatchStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 枚举代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static MatchStatusEnum getByCode(String code) {
        for (MatchStatusEnum value : MatchStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取名称
     * 
     * @param code 枚举代码
     * @return 对应的名称，如果不存在则返回null
     */
    public static String getNameByCode(String code) {
        MatchStatusEnum status = getByCode(code);
        return status != null ? status.getName() : null;
    }
}
