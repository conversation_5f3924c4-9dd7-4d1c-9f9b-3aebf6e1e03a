package cn.savas.hub.module.algocell.enums;


import cn.savas.hub.framework.common.exception.ErrorCode;

/**
 * 知识库匹配系统 错误码枚举类
 *
 * algocell 系统，使用 1-025-000-000 段
 */
public interface ErrorCodeConstants {
    // 权限验证失败
    ErrorCode AUTHORIZATION_FAILED = new ErrorCode(1_025_000_001, "验证失败");

    // 知识库相关错误
    ErrorCode KB_NOT_FOUND = new ErrorCode(1_025_001_001, "知识库不存在");
    ErrorCode KB_LOAD_FAILED = new ErrorCode(1_025_001_002, "知识库加载失败");
    ErrorCode KB_VERSION_MISMATCH = new ErrorCode(1_025_001_003, "知识库版本不匹配");

    // 匹配相关错误
    ErrorCode MATCH_PARAM_INVALID = new ErrorCode(1_025_002_001, "匹配参数无效");
    ErrorCode MATCH_TIMEOUT = new ErrorCode(1_025_002_002, "匹配超时");
    ErrorCode MATCH_FAILED = new ErrorCode(1_025_002_003, "匹配失败");

    // 指标相关错误
    ErrorCode INDICATOR_NOT_FOUND = new ErrorCode(1_025_003_001, "指标不存在");
    ErrorCode INDICATOR_CONFIG_INVALID = new ErrorCode(1_025_003_002, "指标配置无效");

}
