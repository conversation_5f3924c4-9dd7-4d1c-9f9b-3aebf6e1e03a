package cn.savas.hub.module.algocell.enums;

import lombok.Getter;

/**
 * 匹配模式枚举
 * 
 * 定义指标要素的匹配策略，支持精确匹配、层级匹配等多种模式
 * 用于实现"Q345R→碳钢"这类行业先验知识的灵活匹配
 * 
 * <AUTHOR>
 */
@Getter
public enum MatchModeEnum {
    
    /**
     * 精确匹配 - 必须完全匹配指定的要素值
     * 适用于严格的规格要求，如压力等级CL150必须精确匹配
     */
    EXACT("EXACT", "精确匹配"),
    
    /**
     * 祖先匹配 - 匹配指定值或其任意子值
     * 例如：要求"碳钢"，命中"Q345R"（碳钢的子类）也算匹配
     * 适用于材质分类等有层级关系的要素
     */
    ANCESTOR_OK("ANCESTOR_OK", "祖先匹配"),
    
    /**
     * 后代匹配 - 匹配指定值或其任意父值  
     * 例如：要求"Q345R"，命中"碳钢"（Q345R的父类）也算匹配
     * 适用于从具体到抽象的匹配场景
     */
    DESCENDANT_OK("DESCENDANT_OK", "后代匹配"),
    
    /**
     * 任意匹配 - 该要素任意值出现即可
     * 适用于只要求存在某个要素，不限制具体值的场景
     */
    ANY("ANY", "任意匹配");
    
    /**
     * 枚举代码
     */
    private final String code;
    
    /**
     * 枚举名称
     */
    private final String name;
    
    MatchModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 枚举代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static MatchModeEnum getByCode(String code) {
        for (MatchModeEnum value : MatchModeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取名称
     * 
     * @param code 枚举代码
     * @return 对应的名称，如果不存在则返回null
     */
    public static String getNameByCode(String code) {
        MatchModeEnum matchMode = getByCode(code);
        return matchMode != null ? matchMode.getName() : null;
    }
}
