package cn.savas.hub.module.algocell.enums;

import lombok.Getter;

/**
 * 术语类型枚举
 * 
 * 用于区分知识库中术语的匹配方式：字符串精确匹配或正则表达式匹配
 * 
 * <AUTHOR>
 */
@Getter
public enum TermTypeEnum {
    
    /**
     * 字符串类型 - 使用Trie树进行精确匹配
     * 适用于固定词汇、产品型号等精确匹配场景
     */
    STRING("STRING", "字符串匹配"),
    
    /**
     * 正则表达式类型 - 使用预编译的Pattern进行模式匹配
     * 适用于复杂的模式识别，如标准号、规格范围等
     */
    REGEX("REGEX", "正则匹配");
    
    /**
     * 枚举代码
     */
    private final String code;
    
    /**
     * 枚举名称
     */
    private final String name;
    
    TermTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 枚举代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TermTypeEnum getByCode(String code) {
        for (TermTypeEnum value : TermTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取名称
     * 
     * @param code 枚举代码
     * @return 对应的名称，如果不存在则返回null
     */
    public static String getNameByCode(String code) {
        TermTypeEnum termType = getByCode(code);
        return termType != null ? termType.getName() : null;
    }
}
