# AlgoCell Redis 缓存集成说明

## 📋 概述

根据 matching.md 文档第2.2节的要求，AlgoCell模块已完整集成Redis缓存功能，实现了知识库数据的分布式缓存和高可用性。

## 🎯 Redis缓存内容

### 核心缓存键（按文档要求）

| 缓存键 | 说明 | 数据类型 | 过期时间 |
|--------|------|----------|----------|
| `KB:VERSION` | 知识库版本号 | String | 24小时 |
| `KB:FEATURE_WEIGHT` | 要素权重表 | JSON | 24小时 |
| `KB:VAL2IND:{key}` | 要素值到指标的倒排索引 | JSON | 24小时 |
| `KB:TERM2VAL:{hash}` | 术语到要素值的映射 | JSON | 24小时 |
| `KB:MATCH_RESULT:{key}` | 匹配结果缓存 | JSON | 24小时 |

### 扩展缓存功能

- **Trie树序列化**：支持字符串术语Trie的Redis存储
- **正则表达式列表**：缓存预编译的正则表达式
- **匹配结果缓存**：提升重复查询性能

## 🔧 配置说明

### application.yml 配置

```yaml
savas:
  algocell:
    cache:
      enabled: true                    # 启用缓存功能
      enable-redis-cache: true         # 启用Redis缓存
      redis-key-prefix: "KB:"          # Redis键前缀
      redis-expire-hours: 24           # Redis过期时间（小时）
```

### Redis连接配置

AlgoCell模块使用框架提供的Redis配置（`savas-framework/savas-spring-boot-starter-redis`），无需额外配置。

框架已包含：
- RedisTemplate配置
- 连接池配置
- 序列化配置
- 健康检查配置

只需在项目的application.yml中配置Redis连接信息：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_password
    database: 0
```

## 🏗️ 架构设计

### 简化缓存架构

```
请求 → Redis缓存 → 数据库
  ↓         ↓         ↓
 命中     命中      查询
  ↓         ↓         ↓
 返回     返回    回填Redis
```

**架构优势**：
- **简单清晰**：去除多层缓存复杂性
- **数据一致性**：避免本地缓存和Redis不一致问题
- **内存节省**：减少应用服务器内存占用
- **运维简化**：只需管理Redis一套缓存系统

### 降级机制

1. **健康检查**：每30秒检查Redis连接状态
2. **自动降级**：连续3次失败后停用缓存功能
3. **自动恢复**：Redis恢复后自动重新启用
4. **手动控制**：支持手动启用/禁用Redis缓存

## 📊 核心组件

### 1. RedisCacheService
- **功能**：Redis缓存的核心操作服务
- **职责**：知识库数据的Redis存储和检索
- **特性**：JSON序列化、过期时间管理、错误处理

### 2. RedisHealthService
- **功能**：Redis健康检查和降级管理
- **职责**：监控Redis状态、实现自动降级
- **特性**：定时检查、故障计数、状态管理

### 3. MatchCacheService（简化）
- **功能**：统一的缓存管理服务
- **职责**：提供Redis缓存的统一接口
- **特性**：Redis缓存、降级处理、统计信息

### 4. 框架Redis配置
- **功能**：使用savas-framework提供的Redis配置
- **职责**：标准化的RedisTemplate和连接管理
- **特性**：开箱即用、统一配置、性能优化

## 🚀 使用示例

### 基本缓存操作

```java
@Resource
private RedisCacheService redisCacheService;

// 缓存知识库版本
redisCacheService.cacheKbVersion("KB_20231201_001");

// 获取缓存版本
String version = redisCacheService.getCachedKbVersion();

// 缓存要素权重
Map<String, Double> weights = Map.of("material", 1.2, "pressure", 1.5);
redisCacheService.cacheFeatureWeights(weights);

// 清理所有缓存
redisCacheService.clearKbCache();
```

### 健康检查

```java
@Resource
private RedisHealthService redisHealthService;

// 检查Redis状态
boolean available = redisHealthService.isRedisAvailable();

// 获取健康信息
RedisHealthService.RedisHealthInfo info = redisHealthService.getHealthInfo();

// 手动设置状态
redisHealthService.setRedisAvailable(false);
```

### 匹配缓存

```java
@Resource
private MatchCacheService matchCacheService;

// 获取匹配结果（直接从Redis获取）
List<MatchResult> results = matchCacheService.getMatchResults(cacheKey);

// 缓存匹配结果（直接存储到Redis）
matchCacheService.putMatchResults(cacheKey, results);

// 获取标准化文本缓存
String normalized = matchCacheService.getNormalizedText(originalText);

// 缓存标准化文本
matchCacheService.putNormalizedText(originalText, normalizedText);
```

## 🔍 监控和管理

### REST API接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/algocell/cache/statistics` | GET | 获取缓存统计信息 |
| `/algocell/cache/clear` | POST | 清理所有缓存 |
| `/algocell/cache/clear/redis` | POST | 清理Redis缓存 |
| `/algocell/cache/redis/health` | GET | 获取Redis健康状态 |
| `/algocell/cache/redis/enable` | POST | 启用Redis缓存 |
| `/algocell/cache/redis/disable` | POST | 禁用Redis缓存 |

### 统计信息

```json
{
  "redis_enabled": true,
  "redis_available": true,
  "cache_type": "REDIS_ONLY",
  "match_result_cache_hit": 150,
  "normalization_cache_hit": 45,
  "match_result_cache_miss": 20,
  "normalization_cache_miss": 5,
  "hit_rate": 0.87,
  "total_requests": 220,
  "total_hits": 195,
  "total_misses": 25
}
```

## ⚡ 性能优化

### 缓存策略

1. **Redis直连**：直接使用Redis缓存，简化架构
2. **批量操作**：支持批量缓存操作，提升效率
3. **智能过期**：根据数据特性设置不同过期时间
4. **键值优化**：使用哈希值优化键名长度

### 内存管理

1. **Redis管理**：由Redis负责内存管理和淘汰策略
2. **压缩存储**：JSON序列化减少存储空间
3. **定期清理**：定期清理过期和无效缓存
4. **监控告警**：监控Redis内存使用情况

## 🛡️ 容错机制

### 故障处理

1. **连接超时**：设置合理的连接和操作超时时间
2. **重试机制**：关键操作支持自动重试
3. **降级处理**：Redis不可用时停用缓存功能，直接查询数据库
4. **错误隔离**：缓存错误不影响核心业务功能

### 数据一致性

1. **版本控制**：通过版本号确保数据一致性
2. **原子操作**：使用Redis事务保证操作原子性
3. **定期同步**：定期检查和同步缓存数据
4. **手动刷新**：支持手动刷新缓存数据

## 📈 扩展功能

### 集群支持

- 支持Redis集群模式
- 支持Redis哨兵模式
- 支持多数据中心部署

### 监控集成

- 集成Prometheus监控
- 支持自定义监控指标
- 提供Grafana仪表板

### 安全特性

- 支持Redis密码认证
- 支持SSL/TLS加密
- 支持访问控制列表

## 🔧 故障排查

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 检查网络连通性

2. **缓存命中率低**
   - 检查缓存键生成逻辑
   - 调整过期时间设置
   - 分析缓存使用模式

3. **内存使用过高**
   - 检查缓存大小配置
   - 分析缓存数据结构
   - 优化序列化方式

### 调试工具

- Redis CLI命令行工具
- 缓存统计API接口
- 健康检查端点
- 日志分析工具

## 📝 最佳实践

1. **合理配置**：根据业务需求配置缓存大小和过期时间
2. **监控告警**：设置Redis状态监控和告警机制
3. **定期维护**：定期清理无效缓存和检查Redis状态
4. **性能测试**：定期进行缓存性能测试和优化
5. **文档更新**：及时更新缓存相关的文档和配置说明
